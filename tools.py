
import os
import subprocess

def list_files(path="."):
    """List all files in a directory"""
    return "\n".join(os.listdir(path))

def run_shell(command):
    """Run a shell command"""
    result = subprocess.run(command, shell=True, capture_output=True)
    return result.stdout.decode() or result.stderr.decode()

def read_file(path):
    """Read the contents of a text file"""
    with open(path, 'r') as f:
        return f.read()
