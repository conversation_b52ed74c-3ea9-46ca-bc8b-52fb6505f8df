
"""
Comprehensive Tool System for Local Agent Mode

Provides file operations, system commands, and utility functions
with proper error handling, safety checks, and configuration support.
"""

import subprocess
import shutil
import logging
from pathlib import Path
from typing import Dict, Optional
from datetime import datetime
from config_manager import get_tool_config, is_tool_enabled


class ToolError(Exception):
    """Custom exception for tool errors"""
    pass


class SafetyError(ToolError):
    """Exception for safety violations"""
    pass


def _check_tool_enabled(tool_name: str) -> None:
    """Check if a tool is enabled"""
    if not is_tool_enabled(tool_name):
        raise ToolError(f"Tool '{tool_name}' is not enabled")


def _validate_path(path: str, must_exist: bool = False, must_be_file: bool = False,
                  must_be_dir: bool = False) -> Path:
    """Validate and normalize a file path"""
    try:
        path_obj = Path(path).resolve()

        if must_exist and not path_obj.exists():
            raise ToolError(f"Path does not exist: {path}")

        if must_be_file and path_obj.exists() and not path_obj.is_file():
            raise ToolError(f"Path is not a file: {path}")

        if must_be_dir and path_obj.exists() and not path_obj.is_dir():
            raise ToolError(f"Path is not a directory: {path}")

        return path_obj

    except Exception as e:
        raise ToolError(f"Invalid path '{path}': {e}")


def _check_file_safety(path: Path, operation: str = "read") -> None:
    """Check file safety constraints"""
    config = get_tool_config("file")

    if operation in ["read", "write"] and path.exists():
        # Check file size
        if path.stat().st_size > config.get("max_file_size", 10485760):
            raise SafetyError(f"File too large: {path}")

        # Check file extension
        allowed_extensions = config.get("allowed_extensions", [])
        if allowed_extensions and path.suffix not in allowed_extensions:
            raise SafetyError(f"File extension not allowed: {path.suffix}")


def _backup_file(path: Path) -> Optional[Path]:
    """Create a backup of a file before modification"""
    config = get_tool_config("file")

    if not config.get("backup_on_write", True) or not path.exists():
        return None

    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = path.with_suffix(f"{path.suffix}.backup_{timestamp}")
        shutil.copy2(path, backup_path)
        logging.info(f"Created backup: {backup_path}")
        return backup_path
    except Exception as e:
        logging.warning(f"Failed to create backup for {path}: {e}")
        return None


# File Operations
def list_files(path: str = ".", pattern: str = "*", recursive: bool = False,
               show_hidden: bool = False, detailed: bool = False) -> str:
    """
    List files and directories in a path

    Args:
        path: Directory path to list (default: current directory)
        pattern: Glob pattern to match files (default: all files)
        recursive: Whether to list recursively (default: False)
        show_hidden: Whether to show hidden files (default: False)
        detailed: Whether to show detailed info (size, date, etc.)

    Returns:
        Formatted string listing of files and directories
    """
    _check_tool_enabled("list_files")

    try:
        path_obj = _validate_path(path, must_exist=True, must_be_dir=True)

        if recursive:
            glob_pattern = f"**/{pattern}"
            files = list(path_obj.glob(glob_pattern))
        else:
            files = list(path_obj.glob(pattern))

        # Filter hidden files if requested
        if not show_hidden:
            files = [f for f in files if not f.name.startswith('.')]

        # Sort files: directories first, then files
        files.sort(key=lambda x: (x.is_file(), x.name.lower()))

        if not files:
            return f"No files found in {path} matching pattern '{pattern}'"

        result_lines = []
        for file_path in files:
            try:
                if detailed:
                    stat = file_path.stat()
                    size = stat.st_size
                    mtime = datetime.fromtimestamp(stat.st_mtime).strftime("%Y-%m-%d %H:%M")
                    file_type = "DIR" if file_path.is_dir() else "FILE"
                    result_lines.append(f"{file_type:4} {size:>10} {mtime} {file_path.name}")
                else:
                    prefix = "/" if file_path.is_dir() else ""
                    result_lines.append(f"{file_path.name}{prefix}")
            except (OSError, PermissionError):
                result_lines.append(f"{file_path.name} (access denied)")

        return "\n".join(result_lines)

    except Exception as e:
        error_msg = f"Error listing files in '{path}': {e}"
        logging.error(error_msg)
        return error_msg


def read_file(path: str, encoding: str = "utf-8", max_lines: Optional[int] = None) -> str:
    """
    Read the contents of a text file

    Args:
        path: Path to the file to read
        encoding: Text encoding (default: utf-8)
        max_lines: Maximum number of lines to read (default: all)

    Returns:
        File contents as string
    """
    _check_tool_enabled("read_file")

    try:
        path_obj = _validate_path(path, must_exist=True, must_be_file=True)
        _check_file_safety(path_obj, "read")

        with open(path_obj, 'r', encoding=encoding) as f:
            if max_lines:
                lines = []
                for i, line in enumerate(f):
                    if i >= max_lines:
                        lines.append(f"... (truncated after {max_lines} lines)")
                        break
                    lines.append(line.rstrip('\n\r'))
                return '\n'.join(lines)
            else:
                return f.read()

    except Exception as e:
        error_msg = f"Error reading file '{path}': {e}"
        logging.error(error_msg)
        return error_msg


def write_file(path: str, content: str, encoding: str = "utf-8",
               append: bool = False, create_dirs: bool = True) -> str:
    """
    Write content to a file

    Args:
        path: Path to the file to write
        content: Content to write to the file
        encoding: Text encoding (default: utf-8)
        append: Whether to append to existing file (default: False)
        create_dirs: Whether to create parent directories (default: True)

    Returns:
        Success message or error description
    """
    _check_tool_enabled("write_file")

    try:
        path_obj = _validate_path(path)

        # Create parent directories if needed
        if create_dirs and not path_obj.parent.exists():
            path_obj.parent.mkdir(parents=True, exist_ok=True)

        # Check safety constraints
        _check_file_safety(path_obj, "write")

        # Create backup if file exists and we're overwriting
        if not append and path_obj.exists():
            _backup_file(path_obj)

        mode = 'a' if append else 'w'
        with open(path_obj, mode, encoding=encoding) as f:
            f.write(content)

        action = "appended to" if append else "written to"
        size = len(content.encode(encoding))
        return f"Successfully {action} {path} ({size} bytes)"

    except Exception as e:
        error_msg = f"Error writing to file '{path}': {e}"
        logging.error(error_msg)
        return error_msg


def search_files(path: str = ".", query: str = "", file_pattern: str = "*",
                case_sensitive: bool = False, max_results: int = 100) -> str:
    """
    Search for text within files

    Args:
        path: Directory to search in
        query: Text to search for
        file_pattern: File pattern to match (default: all files)
        case_sensitive: Whether search is case sensitive
        max_results: Maximum number of results to return

    Returns:
        Formatted search results
    """
    _check_tool_enabled("search_files")

    if not query:
        return "Error: Search query cannot be empty"

    try:
        path_obj = _validate_path(path, must_exist=True, must_be_dir=True)

        results = []
        files_searched = 0

        for file_path in path_obj.rglob(file_pattern):
            if not file_path.is_file():
                continue

            try:
                _check_file_safety(file_path, "read")

                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    for line_num, line in enumerate(f, 1):
                        search_line = line if case_sensitive else line.lower()
                        search_query = query if case_sensitive else query.lower()

                        if search_query in search_line:
                            results.append({
                                'file': str(file_path.relative_to(path_obj)),
                                'line': line_num,
                                'content': line.strip()
                            })

                            if len(results) >= max_results:
                                break

                files_searched += 1
                if len(results) >= max_results:
                    break

            except (PermissionError, UnicodeDecodeError, OSError):
                continue  # Skip files we can't read

        if not results:
            return f"No matches found for '{query}' in {files_searched} files"

        result_lines = [f"Found {len(results)} matches in {files_searched} files:"]
        for result in results:
            result_lines.append(f"{result['file']}:{result['line']}: {result['content']}")

        return "\n".join(result_lines)

    except Exception as e:
        error_msg = f"Error searching files: {e}"
        logging.error(error_msg)
        return error_msg


def create_directory(path: str, parents: bool = True, exist_ok: bool = True) -> str:
    """
    Create a directory

    Args:
        path: Directory path to create
        parents: Whether to create parent directories
        exist_ok: Whether to ignore if directory already exists

    Returns:
        Success message or error description
    """
    _check_tool_enabled("create_directory")

    try:
        path_obj = _validate_path(path)
        path_obj.mkdir(parents=parents, exist_ok=exist_ok)

        if path_obj.exists():
            return f"Directory created successfully: {path}"
        else:
            return f"Failed to create directory: {path}"

    except Exception as e:
        error_msg = f"Error creating directory '{path}': {e}"
        logging.error(error_msg)
        return error_msg


def delete_file(path: str, force: bool = False) -> str:
    """
    Delete a file or directory

    Args:
        path: Path to delete
        force: Whether to force deletion of directories

    Returns:
        Success message or error description
    """
    _check_tool_enabled("delete_file")

    try:
        path_obj = _validate_path(path, must_exist=True)

        if path_obj.is_file():
            # Create backup before deletion
            _backup_file(path_obj)
            path_obj.unlink()
            return f"File deleted successfully: {path}"
        elif path_obj.is_dir():
            if force:
                shutil.rmtree(path_obj)
                return f"Directory deleted successfully: {path}"
            else:
                try:
                    path_obj.rmdir()  # Only works if empty
                    return f"Empty directory deleted successfully: {path}"
                except OSError:
                    return f"Directory not empty. Use force=True to delete: {path}"
        else:
            return f"Path is neither file nor directory: {path}"

    except Exception as e:
        error_msg = f"Error deleting '{path}': {e}"
        logging.error(error_msg)
        return error_msg


def get_file_info(path: str) -> str:
    """
    Get detailed information about a file or directory

    Args:
        path: Path to examine

    Returns:
        Formatted file information
    """
    _check_tool_enabled("get_file_info")

    try:
        path_obj = _validate_path(path, must_exist=True)
        stat = path_obj.stat()

        info_lines = [
            f"Path: {path_obj}",
            f"Type: {'Directory' if path_obj.is_dir() else 'File'}",
            f"Size: {stat.st_size} bytes",
            f"Modified: {datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')}",
            f"Modified (metadata): {datetime.fromtimestamp(stat.st_ctime).strftime('%Y-%m-%d %H:%M:%S')}",
            f"Permissions: {oct(stat.st_mode)[-3:]}"
        ]

        if path_obj.is_file():
            info_lines.append(f"Extension: {path_obj.suffix}")

            # Try to determine if it's a text file
            try:
                with open(path_obj, 'rb') as f:
                    sample = f.read(1024)
                    is_text = all(byte < 128 for byte in sample)
                    info_lines.append(f"Text file: {'Yes' if is_text else 'No'}")
            except:
                pass

        elif path_obj.is_dir():
            try:
                contents = list(path_obj.iterdir())
                dirs = sum(1 for item in contents if item.is_dir())
                files = sum(1 for item in contents if item.is_file())
                info_lines.append(f"Contents: {dirs} directories, {files} files")
            except PermissionError:
                info_lines.append("Contents: Access denied")

        return "\n".join(info_lines)

    except Exception as e:
        error_msg = f"Error getting info for '{path}': {e}"
        logging.error(error_msg)
        return error_msg


# System Operations
def run_shell(command: str, timeout: Optional[int] = None,
              working_dir: Optional[str] = None, safe_mode: bool = True) -> str:
    """
    Execute a shell command

    Args:
        command: Shell command to execute
        timeout: Command timeout in seconds
        working_dir: Working directory for command execution
        safe_mode: Whether to enforce safety restrictions

    Returns:
        Command output or error message
    """
    _check_tool_enabled("run_shell")

    config = get_tool_config("shell")

    if timeout is None:
        timeout = config.get("timeout", 30)

    if safe_mode and config.get("safe_mode", True):
        allowed_commands = config.get("allowed_commands", [])
        command_parts = command.strip().split()

        if command_parts and allowed_commands:
            base_command = command_parts[0]
            if base_command not in allowed_commands:
                return f"Command '{base_command}' not allowed in safe mode. Allowed: {', '.join(allowed_commands)}"

    try:
        # Validate working directory if provided
        if working_dir:
            work_path = _validate_path(working_dir, must_exist=True, must_be_dir=True)
            working_dir = str(work_path)

        logging.info(f"Executing command: {command}")

        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=timeout,
            cwd=working_dir
        )

        output_lines = []

        if result.stdout:
            output_lines.append("STDOUT:")
            output_lines.append(result.stdout.strip())

        if result.stderr:
            output_lines.append("STDERR:")
            output_lines.append(result.stderr.strip())

        if result.returncode != 0:
            output_lines.append(f"Exit code: {result.returncode}")

        return "\n".join(output_lines) if output_lines else "Command completed with no output"

    except subprocess.TimeoutExpired:
        error_msg = f"Command timed out after {timeout} seconds: {command}"
        logging.error(error_msg)
        return error_msg
    except Exception as e:
        error_msg = f"Error executing command '{command}': {e}"
        logging.error(error_msg)
        return error_msg


# Tool Registry
TOOL_REGISTRY = {
    "list_files": list_files,
    "read_file": read_file,
    "write_file": write_file,
    "search_files": search_files,
    "create_directory": create_directory,
    "delete_file": delete_file,
    "get_file_info": get_file_info,
    "run_shell": run_shell,
}


def get_tool_registry() -> Dict[str, callable]:
    """Get the complete tool registry"""
    return TOOL_REGISTRY


def get_tool_descriptions() -> Dict[str, str]:
    """Get descriptions of all available tools"""
    descriptions = {}
    for name, func in TOOL_REGISTRY.items():
        if is_tool_enabled(name):
            descriptions[name] = func.__doc__ or "No description available"
    return descriptions


def execute_tool(tool_name: str, **kwargs) -> str:
    """
    Execute a tool by name with given arguments

    Args:
        tool_name: Name of the tool to execute
        **kwargs: Arguments to pass to the tool

    Returns:
        Tool execution result
    """
    if tool_name not in TOOL_REGISTRY:
        return f"Unknown tool: {tool_name}"

    if not is_tool_enabled(tool_name):
        return f"Tool '{tool_name}' is not enabled"

    try:
        tool_func = TOOL_REGISTRY[tool_name]
        result = tool_func(**kwargs)
        logging.info(f"Tool '{tool_name}' executed successfully")
        return result
    except Exception as e:
        error_msg = f"Error executing tool '{tool_name}': {e}"
        logging.error(error_msg)
        return error_msg
