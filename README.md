# Local Agent Mode System

A comprehensive, offline-capable AI agent system that runs entirely on your local machine using Ollama and open-source models. This system provides intelligent task planning, execution, and reflection capabilities similar to advanced AI assistants.

## 🚀 Features

- **Fully Offline**: Runs entirely on your local machine with no external API calls
- **Configurable Autonomy**: Three levels of operation (manual, confirm, auto)
- **Intelligent Planning**: Breaks down complex requests into executable steps
- **Error Recovery**: Automatic retry logic and intelligent error handling
- **Memory System**: SQLite-based memory with context and long-term fact storage
- **Safety First**: Built-in safety checks and user confirmations for risky operations
- **Extensible Tools**: Modular tool system for easy extension
- **Rich Logging**: Comprehensive logging and debugging capabilities

## 📋 Requirements

- **System**: 12GB RAM minimum, works with CPU or GPU
- **Python**: 3.8 or higher
- **Ollama**: Installed and running locally
- **Model**: Compatible with Mistral, Phi-2, Code Llama, or similar models

## 🛠️ Installation

1. **Install Ollama** (if not already installed):
   ```bash
   # Visit https://ollama.ai for installation instructions
   # Then pull a model:
   ollama pull mistral
   ```

2. **Clone or download this repository**:
   ```bash
   git clone <repository-url>
   cd agent_mode
   ```

3. **Install Python dependencies**:
   ```bash
   pip install pyyaml
   ```

4. **Run the agent**:
   ```bash
   python agent.py
   ```

## 🎯 Quick Start

### Basic Usage

```bash
python agent.py
```

The agent will start in interactive mode. Try these example commands:

```
🧠 Agent> list files in the current directory
🧠 Agent> read the README.md file
🧠 Agent> search for 'TODO' in all Python files
🧠 Agent> create a new directory called 'backup'
🧠 Agent> autonomy auto
🧠 Agent> help
```

### Autonomy Levels

- **Manual**: Asks for confirmation before each step
- **Confirm**: Asks for confirmation only for risky operations
- **Auto**: Executes all steps automatically

Change autonomy level during runtime:
```
🧠 Agent> autonomy manual
🧠 Agent> autonomy confirm
🧠 Agent> autonomy auto
```

## 🔧 Configuration

Edit `config.yaml` to customize the agent behavior:

```yaml
# Agent Configuration
agent:
  autonomy_level: "confirm"  # manual, confirm, auto
  max_retries: 3
  max_steps_per_task: 10

# LLM Configuration
llm:
  model: "mistral"  # Change to your preferred model
  temperature: 0.1
  timeout: 30

# Memory Configuration
memory:
  type: "sqlite"  # json, sqlite
  max_context_entries: 50
  max_long_term_facts: 1000
```

## 🛠️ Available Tools

The agent comes with these built-in tools:

- **list_files**: List files and directories with filtering options
- **read_file**: Read text files with encoding support
- **write_file**: Write content to files with backup creation
- **search_files**: Search for text within files using patterns
- **create_directory**: Create directories with parent creation
- **delete_file**: Delete files and directories with safety checks
- **get_file_info**: Get detailed file/directory information
- **run_shell**: Execute shell commands with safety restrictions

## 📝 Example Interactions

### File Management
```
🧠 Agent> list all Python files in the current directory
🧠 Agent> read the config.yaml file and show me the LLM settings
🧠 Agent> create a backup directory and copy all .py files there
```

### Code Analysis
```
🧠 Agent> search for all TODO comments in Python files
🧠 Agent> find all functions that contain 'execute' in their name
🧠 Agent> show me the structure of the tools.py file
```

### System Operations
```
🧠 Agent> check the current directory size
🧠 Agent> find all files modified in the last 24 hours
🧠 Agent> create a summary of all log files
```

## 🔒 Safety Features

- **Tool Validation**: All tools are validated before execution
- **File Size Limits**: Prevents reading/writing extremely large files
- **Extension Filtering**: Restricts operations to safe file types
- **Command Whitelisting**: Shell commands are restricted in safe mode
- **Backup Creation**: Automatic backups before file modifications
- **User Confirmations**: Asks for approval on risky operations

## 🧠 Memory System

The agent maintains two types of memory:

### Context Memory
- Recent conversation history
- Tool execution results
- User preferences and settings

### Long-term Facts
Store and retrieve named facts:
```python
# The agent automatically learns and stores facts like:
memory.set_fact("project_path", "/home/<USER>/my_project")
memory.set_fact("preferred_editor", "vscode")
```

## 🔧 Extending the System

### Adding New Tools

1. **Create a new tool function** in `tools.py`:
```python
def my_custom_tool(param1: str, param2: int = 10) -> str:
    """Description of what this tool does"""
    try:
        # Your tool logic here
        return "Success message"
    except Exception as e:
        return f"Error: {e}"
```

2. **Register the tool** in the `TOOL_REGISTRY`:
```python
TOOL_REGISTRY["my_custom_tool"] = my_custom_tool
```

3. **Enable the tool** in `config.yaml`:
```yaml
tools:
  enabled:
    - my_custom_tool
```

### Custom Prompts

Modify `prompts.py` to customize how the agent reasons and plans:

```python
def create_custom_prompt(user_input: str) -> str:
    return f"""
    Custom prompt template for: {user_input}
    
    Your custom instructions here...
    """
```

## 📊 Monitoring and Debugging

### Logs
Check `agent.log` for detailed execution logs:
```bash
tail -f agent.log
```

### Memory Inspection
```
🧠 Agent> memory
```

### Tool Information
```
🧠 Agent> tools
```

## 🚨 Troubleshooting

### Common Issues

1. **Ollama not found**:
   - Ensure Ollama is installed and in your PATH
   - Try: `ollama --version`

2. **Model not available**:
   - Pull the model: `ollama pull mistral`
   - Check available models: `ollama list`

3. **Permission errors**:
   - Check file permissions
   - Run with appropriate user privileges

4. **Memory issues**:
   - Reduce `max_context_entries` in config
   - Switch to JSON memory if SQLite fails

### Debug Mode

Enable debug logging in `config.yaml`:
```yaml
logging:
  level: "DEBUG"
```

## 🤝 Contributing

This is a modular system designed for easy extension:

1. **Tools**: Add new capabilities in `tools.py`
2. **Memory**: Extend memory types in `memory_system.py`
3. **Planning**: Improve planning logic in `planner.py`
4. **Prompts**: Enhance reasoning in `prompts.py`

## 📄 License

This project is open source. Feel free to modify and extend it for your needs.

## 🙏 Acknowledgments

- Built for offline, local AI agent capabilities
- Inspired by advanced AI assistant systems
- Uses Ollama for local LLM inference
- Designed for privacy and control
