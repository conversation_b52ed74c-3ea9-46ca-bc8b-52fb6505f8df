
# Agent Mode Configuration
agent:
  name: "LocalAgent"
  version: "1.0.0"
  autonomy_level: "confirm"  # manual, confirm, auto
  max_retries: 3
  max_steps_per_task: 10

# LLM Configuration
llm:
  model: "mistral"
  temperature: 0.1
  max_tokens: 2048
  timeout: 30

# Memory Configuration
memory:
  type: "sqlite"  # json, sqlite
  max_context_entries: 50
  max_long_term_facts: 1000
  database_path: "agent_memory.db"
  json_backup_path: "memory.json"

# Tool Configuration
tools:
  enabled:
    - list_files
    - read_file
    - write_file
    - run_shell
    - search_files
    - create_directory
    - delete_file
    - get_file_info

  # Tool-specific settings
  shell:
    timeout: 30
    safe_mode: true
    allowed_commands: ["ls", "pwd", "cat", "grep", "find", "python", "pip"]

  file:
    max_file_size: 10485760  # 10MB
    allowed_extensions: [".txt", ".py", ".json", ".yaml", ".md", ".csv"]
    backup_on_write: true

# Logging Configuration
logging:
  level: "INFO"
  file: "agent.log"
  max_size: 10485760  # 10MB
  backup_count: 3
