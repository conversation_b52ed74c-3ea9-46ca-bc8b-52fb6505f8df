# WSL Setup Guide for Local Agent Mode

Complete guide for running the Local Agent Mode system on Windows WSL (Windows Subsystem for Linux).

## 🚀 Quick WSL Setup

### Option 1: Automated Setup (Recommended)
```bash
# Make the setup script executable and run it
chmod +x setup_wsl.sh
./setup_wsl.sh
```

### Option 2: Manual Setup
Follow the step-by-step instructions below.

## 📋 Prerequisites

### 1. Install WSL
```powershell
# In Windows PowerShell (as Administrator)
wsl --install Ubuntu
# Restart your computer when prompted
```

### 2. Update WSL (if already installed)
```powershell
wsl --update
wsl --set-default-version 2
```

### 3. Access WSL
```powershell
# Start WSL
wsl
# Or open Ubuntu from Start Menu
```

## 🛠️ Manual Installation Steps

### 1. Update System
```bash
sudo apt update && sudo apt upgrade -y
```

### 2. Install Dependencies
```bash
sudo apt install -y python3 python3-pip python3-venv curl wget git sqlite3 build-essential
```

### 3. Install Ollama
```bash
# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Start Ollama service
ollama serve &

# Install a model
ollama pull mistral
```

### 4. Set Up Agent
```bash
# Navigate to your project directory
cd /mnt/c/Users/<USER>/Desktop/agent_mode

# Or copy files to WSL home directory
cp -r /mnt/c/Users/<USER>/Desktop/agent_mode ~/agent_mode
cd ~/agent_mode

# Create virtual environment
python3 -m venv agent_env
source agent_env/bin/activate

# Install dependencies
pip install -r requirements.txt

# Test the setup
python3 setup.py
```

### 5. Run the Agent
```bash
# Activate environment
source agent_env/bin/activate

# Start the agent
python3 agent.py
```

## 🔧 WSL-Specific Configuration

### File System Access
```bash
# Windows files are accessible at:
/mnt/c/Users/<USER>/

# WSL home directory:
~/

# Current working directory in Windows:
/mnt/c/Users/<USER>/Desktop/agent_mode
```

### Environment Variables
```bash
# Add to ~/.bashrc for convenience
echo 'export AGENT_HOME=~/agent_mode' >> ~/.bashrc
echo 'alias agent="cd $AGENT_HOME && source agent_env/bin/activate && python3 agent.py"' >> ~/.bashrc
source ~/.bashrc
```

### Ollama Service Management
```bash
# Start Ollama
ollama serve &

# Check if running
pgrep ollama

# Stop Ollama
pkill ollama

# Check available models
ollama list

# Pull additional models
ollama pull phi
ollama pull codellama
```

## 🚨 Common WSL Issues & Solutions

### Issue 1: "Ollama not found"
```bash
# Check if Ollama is in PATH
which ollama

# If not found, add to PATH
echo 'export PATH=$PATH:/usr/local/bin' >> ~/.bashrc
source ~/.bashrc
```

### Issue 2: "Permission denied"
```bash
# Fix file permissions
chmod +x setup_wsl.sh
chmod +x agent.py

# Or run with python explicitly
python3 agent.py
```

### Issue 3: "Module not found"
```bash
# Ensure virtual environment is activated
source agent_env/bin/activate

# Reinstall dependencies
pip install -r requirements.txt

# Check Python path
python3 -c "import sys; print(sys.path)"
```

### Issue 4: "SQLite database locked"
```bash
# Check for existing processes
ps aux | grep python

# Kill any hanging processes
pkill -f agent.py

# Remove lock file if exists
rm -f agent_memory.db-wal agent_memory.db-shm
```

### Issue 5: "Ollama connection failed"
```bash
# Check Ollama status
curl http://localhost:11434/api/tags

# Restart Ollama
pkill ollama
ollama serve &
sleep 3
ollama list
```

### Issue 6: "Memory/Performance Issues"
```bash
# Check WSL memory usage
free -h

# Restart WSL from Windows PowerShell
wsl --shutdown
wsl

# Reduce memory limits in config.yaml
memory:
  max_context_entries: 20
  max_long_term_facts: 500
```

## 🎯 WSL Performance Tips

### 1. Use WSL File System
```bash
# Better performance when files are in WSL
cp -r /mnt/c/path/to/agent_mode ~/agent_mode
cd ~/agent_mode
```

### 2. Configure WSL Memory
Create/edit `%UserProfile%\.wslconfig`:
```ini
[wsl2]
memory=8GB
processors=4
swap=2GB
```

### 3. Optimize Python
```bash
# Use Python 3.9+ for better performance
sudo apt install python3.9 python3.9-venv python3.9-dev
python3.9 -m venv agent_env
```

## 🔄 Starting the Agent

### Method 1: Direct Command
```bash
cd ~/agent_mode
source agent_env/bin/activate
python3 agent.py
```

### Method 2: Using Startup Script
```bash
cd ~/agent_mode
./start_agent.sh
```

### Method 3: Using Alias (after setup)
```bash
agent
```

## 🧪 Testing in WSL

### Quick Test
```bash
cd ~/agent_mode
source agent_env/bin/activate
python3 examples.py --quick
```

### Full Test Suite
```bash
python3 test_agent.py
```

### Interactive Examples
```bash
python3 examples.py
```

## 🔧 Development in WSL

### VS Code Integration
```bash
# Install VS Code WSL extension
# Open project in VS Code from WSL
code .
```

### Git Configuration
```bash
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"
```

### Python Development
```bash
# Install development tools
pip install black flake8 pytest

# Format code
black *.py

# Lint code
flake8 *.py

# Run tests
pytest test_agent.py
```

## 📊 Monitoring

### System Resources
```bash
# Check memory usage
free -h

# Check disk usage
df -h

# Check running processes
ps aux | grep -E "(python|ollama)"

# Monitor in real-time
htop
```

### Agent Logs
```bash
# View logs
tail -f agent.log

# Search logs
grep "ERROR" agent.log

# Clear logs
> agent.log
```

## 🎉 Success Indicators

When everything is working correctly, you should see:

```bash
$ ollama list
NAME            ID              SIZE    MODIFIED
mistral:latest  abc123def456    4.1GB   2 hours ago

$ python3 agent.py
🤖 Local Agent Mode v1.0.0
Autonomy Level: confirm
Type 'help' for commands, 'exit' to quit

🧠 Agent> help
```

## 🤝 Getting Help

If you encounter issues:

1. **Check the logs**: `tail -f agent.log`
2. **Run diagnostics**: `python3 setup.py`
3. **Test components**: `python3 test_agent.py`
4. **Restart services**: `pkill ollama && ollama serve &`
5. **Restart WSL**: `wsl --shutdown` (from Windows)

The agent should work seamlessly in WSL and often performs better than on native Windows due to the Linux environment's better compatibility with Python and development tools.
