# Getting Started with Local Agent Mode

This guide will help you set up and start using your local AI agent system in just a few minutes.

## 🚀 Quick Start (5 minutes)

### 1. Prerequisites Check
```bash
# Check Python version (3.8+ required)
python --version

# Check if Ollama is installed
ollama --version
```

### 2. Install Ollama (if needed)
```bash
# Visit https://ollama.ai for installation instructions
# Then pull a model:
ollama pull mistral
```

### 3. Set Up the Agent
```bash
# Install dependencies
python setup.py

# Or manually:
pip install -r requirements.txt
```

### 4. Start the Agent
```bash
python agent.py
```

## 🎯 First Commands to Try

Once the agent starts, try these commands:

```
🧠 Agent> help
🧠 Agent> list files in the current directory
🧠 Agent> read the README.md file
🧠 Agent> tools
🧠 Agent> memory
```

## ⚙️ Configuration

### Autonomy Levels
- **manual**: Asks for confirmation before each step
- **confirm**: Asks for confirmation for risky operations only
- **auto**: Executes automatically

Change during runtime:
```
🧠 Agent> autonomy manual
🧠 Agent> autonomy confirm
🧠 Agent> autonomy auto
```

### Edit config.yaml
```yaml
agent:
  autonomy_level: "confirm"  # Change default level
  max_retries: 3

llm:
  model: "mistral"  # Change model
  temperature: 0.1
```

## 📝 Example Tasks

### File Management
```
🧠 Agent> create a backup directory
🧠 Agent> copy all Python files to the backup directory
🧠 Agent> list all files larger than 1MB
```

### Code Analysis
```
🧠 Agent> search for all TODO comments in Python files
🧠 Agent> find all functions that contain 'execute' in their name
🧠 Agent> show me the structure of the main agent file
```

### System Operations
```
🧠 Agent> check what Python packages are installed
🧠 Agent> find all files modified today
🧠 Agent> create a summary of all log files
```

## 🔧 Testing

### Run Quick Test
```bash
python examples.py --quick
```

### Run Full Test Suite
```bash
python test_agent.py
```

### Run Specific Examples
```bash
python examples.py --basic    # Basic usage
python examples.py --memory   # Memory system
python examples.py --tools    # Tool usage
python examples.py --files    # File operations
```

## 🚨 Troubleshooting

### Common Issues

**"Ollama not found"**
```bash
# Install Ollama from https://ollama.ai
# Make sure it's in your PATH
ollama --version
```

**"No models available"**
```bash
# Pull a model
ollama pull mistral
# Check available models
ollama list
```

**"Permission denied"**
```bash
# Check file permissions
# Run with appropriate user privileges
```

**"Memory errors"**
```bash
# Edit config.yaml and reduce memory limits
memory:
  max_context_entries: 20  # Reduce from 50
```

### Debug Mode
Enable detailed logging in config.yaml:
```yaml
logging:
  level: "DEBUG"
```

Then check the log file:
```bash
tail -f agent.log
```

## 🎓 Learning More

### Understanding the System
1. **agent.py** - Main orchestrator
2. **tools.py** - Available operations
3. **planner.py** - Task planning logic
4. **memory_system.py** - Context and fact storage
5. **prompts.py** - AI reasoning templates

### Extending the Agent
1. **Add new tools** in tools.py
2. **Modify prompts** in prompts.py
3. **Adjust configuration** in config.yaml
4. **Customize memory** in memory_system.py

### Best Practices
- Start with "confirm" autonomy level
- Use descriptive requests
- Check logs for debugging
- Back up important files before operations
- Test new tools in a safe environment

## 📚 Next Steps

1. **Read the full README.md** for detailed documentation
2. **Try the examples.py** script for guided tutorials
3. **Experiment with different models** (phi-2, codellama, etc.)
4. **Customize the configuration** for your needs
5. **Add your own tools** for specific tasks

## 🤝 Getting Help

- Check the logs: `tail -f agent.log`
- Run diagnostics: `python setup.py`
- Test components: `python test_agent.py`
- Review examples: `python examples.py`

## 🎉 You're Ready!

Your local AI agent is now set up and ready to help with tasks. Start with simple commands and gradually explore more complex operations as you become comfortable with the system.

Remember: This agent runs entirely on your machine, keeping your data private and secure!
