#!/usr/bin/env python3
"""
Example Usage and Testing Script for Local Agent Mode

This script demonstrates various ways to use the agent system
and provides examples for different scenarios.
"""

import sys
import time
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from agent import LocalAgent
from memory_system import get_memory
from tools import execute_tool
from config_manager import get_config


def example_basic_usage():
    """Example of basic agent usage"""
    print("=== Basic Agent Usage Example ===\n")
    
    # Create agent instance
    agent = LocalAgent()
    
    # Example requests
    example_requests = [
        "list files in the current directory",
        "read the README.md file",
        "show me information about the config.yaml file",
    ]
    
    print("This example will process several requests automatically.")
    print("The agent is set to 'auto' mode for demonstration.\n")
    
    # Set to auto mode for examples
    agent.config_manager.update_autonomy_level("auto")
    
    for i, request in enumerate(example_requests, 1):
        print(f"\n--- Example {i}: {request} ---")
        agent.process_request(request)
        time.sleep(2)  # Brief pause between requests


def example_memory_usage():
    """Example of memory system usage"""
    print("\n=== Memory System Example ===\n")
    
    memory = get_memory()
    
    # Store some facts
    facts = {
        "project_name": "Local Agent Mode",
        "version": "1.0.0",
        "author": "AI Assistant",
        "language": "Python",
        "database": "SQLite"
    }
    
    print("Storing facts in memory...")
    for key, value in facts.items():
        memory.set_fact(key, value)
        print(f"  ✓ {key}: {value}")
    
    print("\nRetrieving facts from memory...")
    for key in facts.keys():
        stored_value = memory.get_fact(key)
        print(f"  📖 {key}: {stored_value}")
    
    # Search facts
    print("\nSearching for facts containing 'Agent'...")
    search_results = memory.search_facts("Agent")
    for key, value in search_results:
        print(f"  🔍 {key}: {value}")
    
    # Show context
    print("\nRecent context entries:")
    context = memory.get_context(limit=3)
    for entry in context:
        timestamp = entry.get('timestamp', '')[:19]
        value = entry.get('value', '')[:100]
        print(f"  📝 [{timestamp}] {value}...")


def example_tool_usage():
    """Example of direct tool usage"""
    print("\n=== Direct Tool Usage Example ===\n")
    
    # List of tool examples
    tool_examples = [
        ("list_files", {"detailed": True}),
        ("get_file_info", {"path": "agent.py"}),
        ("search_files", {"query": "def", "file_pattern": "*.py"}),
    ]
    
    for tool_name, args in tool_examples:
        print(f"\n--- Testing {tool_name} ---")
        print(f"Arguments: {args}")
        
        result = execute_tool(tool_name, **args)
        print(f"Result: {result[:200]}...")


def example_configuration():
    """Example of configuration usage"""
    print("\n=== Configuration Example ===\n")
    
    config = get_config()
    
    print("Current configuration:")
    print(f"  Agent name: {config.agent.name}")
    print(f"  Autonomy level: {config.agent.autonomy_level}")
    print(f"  LLM model: {config.llm.model}")
    print(f"  Memory type: {config.memory.type}")
    print(f"  Max retries: {config.agent.max_retries}")
    
    print(f"\nEnabled tools: {', '.join(config.tools.enabled)}")


def example_interactive_session():
    """Example of an interactive session"""
    print("\n=== Interactive Session Example ===\n")
    
    print("This would start an interactive session.")
    print("In a real scenario, you would run:")
    print("  python agent.py")
    print("\nThen you could type commands like:")
    print("  🧠 Agent> list files in the current directory")
    print("  🧠 Agent> autonomy manual")
    print("  🧠 Agent> help")
    print("  🧠 Agent> memory")
    print("  🧠 Agent> tools")
    print("  🧠 Agent> exit")


def example_error_handling():
    """Example of error handling"""
    print("\n=== Error Handling Example ===\n")
    
    # Try to read a non-existent file
    print("Testing error handling with non-existent file...")
    result = execute_tool("read_file", path="nonexistent_file.txt")
    print(f"Result: {result}")
    
    # Try an invalid tool
    print("\nTesting with invalid tool...")
    result = execute_tool("invalid_tool", some_arg="value")
    print(f"Result: {result}")


def example_file_operations():
    """Example of file operations"""
    print("\n=== File Operations Example ===\n")
    
    test_dir = Path("test_example")
    test_file = test_dir / "example.txt"
    
    try:
        # Create directory
        print("Creating test directory...")
        result = execute_tool("create_directory", path=str(test_dir))
        print(f"  {result}")
        
        # Write file
        print("Writing test file...")
        content = "This is a test file created by the agent system.\nIt demonstrates file operations."
        result = execute_tool("write_file", path=str(test_file), content=content)
        print(f"  {result}")
        
        # Read file
        print("Reading test file...")
        result = execute_tool("read_file", path=str(test_file))
        print(f"  Content: {result}")
        
        # Get file info
        print("Getting file information...")
        result = execute_tool("get_file_info", path=str(test_file))
        print(f"  Info: {result}")
        
        # Search in file
        print("Searching in file...")
        result = execute_tool("search_files", path=str(test_dir), query="agent")
        print(f"  Search results: {result}")
        
    except Exception as e:
        print(f"Error in file operations: {e}")
    
    finally:
        # Cleanup
        try:
            if test_file.exists():
                execute_tool("delete_file", path=str(test_file))
            if test_dir.exists():
                execute_tool("delete_file", path=str(test_dir), force=True)
            print("Cleanup completed.")
        except:
            pass


def run_all_examples():
    """Run all examples"""
    print("🤖 Local Agent Mode - Examples and Testing")
    print("=" * 50)
    
    try:
        example_configuration()
        example_memory_usage()
        example_tool_usage()
        example_file_operations()
        example_error_handling()
        example_interactive_session()
        
        print("\n" + "=" * 50)
        print("✅ All examples completed successfully!")
        print("\nTo try the full interactive agent, run:")
        print("  python agent.py")
        
    except Exception as e:
        print(f"\n❌ Error running examples: {e}")
        import traceback
        traceback.print_exc()


def run_quick_test():
    """Run a quick test of the agent"""
    print("🧪 Quick Agent Test")
    print("=" * 30)
    
    try:
        agent = LocalAgent()
        agent.config_manager.update_autonomy_level("auto")
        
        # Simple test request
        print("Testing with: 'list files in current directory'")
        agent.process_request("list files in current directory")
        
        print("\n✅ Quick test completed!")
        
    except Exception as e:
        print(f"\n❌ Quick test failed: {e}")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Local Agent Mode Examples")
    parser.add_argument("--quick", action="store_true", help="Run quick test only")
    parser.add_argument("--basic", action="store_true", help="Run basic usage example")
    parser.add_argument("--memory", action="store_true", help="Run memory example")
    parser.add_argument("--tools", action="store_true", help="Run tools example")
    parser.add_argument("--files", action="store_true", help="Run file operations example")
    
    args = parser.parse_args()
    
    if args.quick:
        run_quick_test()
    elif args.basic:
        example_basic_usage()
    elif args.memory:
        example_memory_usage()
    elif args.tools:
        example_tool_usage()
    elif args.files:
        example_file_operations()
    else:
        # Run all examples by default
        run_all_examples()
