# Model Selection Guide for Local Agent Mode

Choose the best AI model for your system based on quality, speed, and resource requirements.

## 🎯 **Recommended Models for 12GB RAM + RX 550**

### **🥇 Best Overall: Qwen2.5:3B**
```bash
ollama pull qwen2.5:3b
```
- **Size**: ~2GB
- **Quality**: ⭐⭐⭐⭐⭐ Excellent reasoning, coding, and planning
- **Speed**: ⭐⭐⭐⭐⭐ Very fast
- **Memory**: ~3GB RAM usage
- **Best for**: General agent tasks, coding, file operations

### **🥈 Alternative: Llama 3.2:3B**
```bash
ollama pull llama3.2:3b
```
- **Size**: ~2GB
- **Quality**: ⭐⭐⭐⭐⭐ Excellent instruction following
- **Speed**: ⭐⭐⭐⭐ Fast
- **Memory**: ~3GB RAM usage
- **Best for**: Complex reasoning, multi-step planning

### **🥉 Lightweight: Phi-3.5:3.8B**
```bash
ollama pull phi3.5:3.8b
```
- **Size**: ~2.3GB
- **Quality**: ⭐⭐⭐⭐ Very good for size
- **Speed**: ⭐⭐⭐⭐⭐ Extremely fast
- **Memory**: ~3.5GB RAM usage
- **Best for**: Quick responses, simple tasks

## 🚀 **Premium Options (if you want maximum quality)**

### **Qwen2.5:7B** (Recommended if you have the resources)
```bash
ollama pull qwen2.5:7b
```
- **Size**: ~4.4GB
- **Quality**: ⭐⭐⭐⭐⭐ Outstanding reasoning and coding
- **Speed**: ⭐⭐⭐ Moderate (still good on your system)
- **Memory**: ~6GB RAM usage
- **Best for**: Complex agent tasks, advanced reasoning

### **Llama 3.2:7B**
```bash
ollama pull llama3.2:7b
```
- **Size**: ~4.4GB
- **Quality**: ⭐⭐⭐⭐⭐ Excellent all-around performance
- **Speed**: ⭐⭐⭐ Moderate
- **Memory**: ~6GB RAM usage
- **Best for**: Advanced planning, complex problem solving

## 🎯 **My Top Recommendation: Qwen2.5:3B**

**Why Qwen2.5:3B is perfect for your agent:**

✅ **Excellent Code Understanding** - Great for file operations and shell commands
✅ **Strong Reasoning** - Perfect for task planning and problem solving  
✅ **Fast Response Time** - Quick enough for interactive use
✅ **Memory Efficient** - Leaves plenty of RAM for other processes
✅ **JSON Output** - Reliable structured output for tool calls
✅ **Instruction Following** - Follows agent prompts precisely

## 🔧 **How to Switch Models**

### Method 1: Update Config File
Edit `config.yaml`:
```yaml
llm:
  model: "qwen2.5:3b"  # Change this line
```

### Method 2: Runtime Command
In the agent:
```
🧠 Agent> I want to use a different model
```

### Method 3: Direct Ollama
```bash
# Install model
ollama pull qwen2.5:3b

# Test model
ollama run qwen2.5:3b "Hello, how are you?"
```

## 📊 **Performance Comparison on Your System**

| Model | Size | RAM Usage | Speed | Quality | Best For |
|-------|------|-----------|-------|---------|----------|
| **Qwen2.5:3B** | 2GB | 3GB | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | **Agent tasks** |
| Llama3.2:3B | 2GB | 3GB | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | Reasoning |
| Phi3.5:3.8B | 2.3GB | 3.5GB | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | Speed |
| Qwen2.5:7B | 4.4GB | 6GB | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | Complex tasks |
| Mistral:7B | 4.1GB | 5.5GB | ⭐⭐⭐ | ⭐⭐⭐⭐ | General use |

## 🎮 **GPU Acceleration (RX 550)**

Your RX 550 can help with inference! Enable GPU acceleration:

```bash
# Check if GPU is detected
ollama run qwen2.5:3b --verbose

# Force GPU usage (if supported)
OLLAMA_GPU_LAYERS=20 ollama run qwen2.5:3b
```

## 🧪 **Testing Your Model**

After installing, test the model:

```bash
# Quick test
ollama run qwen2.5:3b "Create a JSON plan to list files: [{'tool': 'list_files', 'args': {'path': '.'}}]"

# Agent test
python agent.py
🧠 Agent> list files in current directory
```

## 🔄 **Model Management**

```bash
# List installed models
ollama list

# Remove unused models
ollama rm mistral

# Update model
ollama pull qwen2.5:3b

# Check model info
ollama show qwen2.5:3b
```

## 💡 **Pro Tips**

1. **Start with Qwen2.5:3B** - Best balance of quality and speed
2. **Monitor RAM usage** - Use Task Manager to check memory
3. **Test different models** - Each has unique strengths
4. **Keep 2-3 models** - Switch based on task complexity
5. **Use GPU acceleration** - Your RX 550 can help with inference

## 🎯 **Final Recommendation**

**For your Local Agent Mode system, use Qwen2.5:3B**:
- Perfect quality for agent tasks
- Fast enough for interactive use
- Efficient memory usage
- Excellent JSON output reliability
- Strong coding and reasoning capabilities

This model will give you the best experience with your agent system!
