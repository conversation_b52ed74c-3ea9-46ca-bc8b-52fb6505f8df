#!/bin/bash
# WSL Setup Script for Local Agent Mode
# Run this script in WSL to set up the complete environment

set -e  # Exit on any error

echo "🐧 Setting up Local Agent Mode on WSL"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running in WSL
check_wsl() {
    if grep -qi microsoft /proc/version; then
        print_success "Running in WSL environment"
        return 0
    else
        print_warning "Not detected as WSL, but continuing anyway"
        return 0
    fi
}

# Update system packages
update_system() {
    print_status "Updating system packages..."
    sudo apt update && sudo apt upgrade -y
    print_success "System packages updated"
}

# Install required system packages
install_system_deps() {
    print_status "Installing system dependencies..."
    sudo apt install -y \
        python3 \
        python3-pip \
        python3-venv \
        curl \
        wget \
        git \
        sqlite3 \
        build-essential
    print_success "System dependencies installed"
}

# Install Ollama
install_ollama() {
    print_status "Checking for Ollama..."
    
    if command -v ollama &> /dev/null; then
        print_success "Ollama already installed"
        ollama --version
        return 0
    fi
    
    print_status "Installing Ollama..."
    curl -fsSL https://ollama.ai/install.sh | sh
    
    # Add ollama to PATH if needed
    if ! command -v ollama &> /dev/null; then
        echo 'export PATH=$PATH:/usr/local/bin' >> ~/.bashrc
        export PATH=$PATH:/usr/local/bin
    fi
    
    print_success "Ollama installed successfully"
}

# Start Ollama service
start_ollama() {
    print_status "Starting Ollama service..."
    
    # Check if already running
    if pgrep -x "ollama" > /dev/null; then
        print_success "Ollama service already running"
        return 0
    fi
    
    # Start Ollama in background
    nohup ollama serve > ollama.log 2>&1 &
    sleep 3
    
    if pgrep -x "ollama" > /dev/null; then
        print_success "Ollama service started"
    else
        print_error "Failed to start Ollama service"
        return 1
    fi
}

# Install a model
install_model() {
    local model=${1:-mistral}
    print_status "Installing model: $model"
    
    # Check if model already exists
    if ollama list | grep -q "$model"; then
        print_success "Model $model already installed"
        return 0
    fi
    
    print_status "Downloading model $model (this may take a while)..."
    ollama pull "$model"
    print_success "Model $model installed successfully"
}

# Set up Python virtual environment
setup_python_env() {
    print_status "Setting up Python virtual environment..."
    
    # Create virtual environment if it doesn't exist
    if [ ! -d "agent_env" ]; then
        python3 -m venv agent_env
        print_success "Virtual environment created"
    else
        print_success "Virtual environment already exists"
    fi
    
    # Activate virtual environment
    source agent_env/bin/activate
    
    # Upgrade pip
    pip install --upgrade pip
    
    # Install requirements
    if [ -f "requirements.txt" ]; then
        pip install -r requirements.txt
        print_success "Python dependencies installed"
    else
        print_warning "requirements.txt not found, installing basic dependencies"
        pip install PyYAML
    fi
}

# Test the installation
test_installation() {
    print_status "Testing installation..."
    
    # Activate virtual environment
    source agent_env/bin/activate
    
    # Test Python components
    python3 -c "
import sys
sys.path.insert(0, '.')
try:
    from config_manager import get_config
    from memory_system import get_memory
    from tools import get_tool_registry
    print('✓ All Python modules imported successfully')
except Exception as e:
    print(f'✗ Error importing modules: {e}')
    sys.exit(1)
"
    
    # Test Ollama
    if ollama list > /dev/null 2>&1; then
        print_success "Ollama is working"
    else
        print_error "Ollama test failed"
        return 1
    fi
    
    print_success "Installation test completed successfully"
}

# Create startup script
create_startup_script() {
    print_status "Creating startup script..."
    
    cat > start_agent.sh << 'EOF'
#!/bin/bash
# Startup script for Local Agent Mode

# Activate virtual environment
source agent_env/bin/activate

# Start Ollama if not running
if ! pgrep -x "ollama" > /dev/null; then
    echo "Starting Ollama service..."
    nohup ollama serve > ollama.log 2>&1 &
    sleep 3
fi

# Start the agent
echo "Starting Local Agent Mode..."
python3 agent.py
EOF
    
    chmod +x start_agent.sh
    print_success "Startup script created: ./start_agent.sh"
}

# Create WSL-specific aliases
create_aliases() {
    print_status "Creating helpful aliases..."
    
    # Add to .bashrc if not already present
    if ! grep -q "# Local Agent Mode aliases" ~/.bashrc; then
        cat >> ~/.bashrc << 'EOF'

# Local Agent Mode aliases
alias agent='cd ~/agent_mode && ./start_agent.sh'
alias agent-test='cd ~/agent_mode && source agent_env/bin/activate && python3 test_agent.py'
alias agent-examples='cd ~/agent_mode && source agent_env/bin/activate && python3 examples.py'
alias ollama-status='pgrep -x ollama > /dev/null && echo "Ollama is running" || echo "Ollama is not running"'
EOF
        print_success "Aliases added to ~/.bashrc"
        print_status "Run 'source ~/.bashrc' or restart terminal to use aliases"
    else
        print_success "Aliases already exist"
    fi
}

# Main installation function
main() {
    echo
    print_status "Starting WSL setup for Local Agent Mode..."
    echo
    
    # Run setup steps
    check_wsl
    update_system
    install_system_deps
    install_ollama
    start_ollama
    install_model "mistral"
    setup_python_env
    test_installation
    create_startup_script
    create_aliases
    
    echo
    print_success "🎉 Setup completed successfully!"
    echo
    echo "Next steps:"
    echo "1. Run: source ~/.bashrc"
    echo "2. Start the agent: ./start_agent.sh"
    echo "3. Or use the alias: agent"
    echo
    echo "Useful commands:"
    echo "  agent          - Start the agent"
    echo "  agent-test     - Run tests"
    echo "  agent-examples - Run examples"
    echo "  ollama-status  - Check Ollama status"
    echo
    print_status "The agent is ready to use! 🤖"
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "WSL Setup Script for Local Agent Mode"
        echo
        echo "Usage: $0 [options]"
        echo
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --model MODEL  Install specific model (default: mistral)"
        echo
        echo "Examples:"
        echo "  $0                    # Install with default model (mistral)"
        echo "  $0 --model phi        # Install with phi model"
        echo "  $0 --model codellama  # Install with codellama model"
        exit 0
        ;;
    --model)
        if [ -n "${2:-}" ]; then
            MODEL="$2"
            shift 2
        else
            print_error "Model name required after --model"
            exit 1
        fi
        ;;
    *)
        MODEL="mistral"
        ;;
esac

# Run main installation
main
