"""
Advanced Memory System for Local Agent Mode

Supports both JSON and SQLite backends for storing:
- Conversation context (recent interactions)
- Long-term facts (named knowledge)
- Tool execution history
- User preferences and settings
"""

import json
import sqlite3
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
from config_manager import get_config


@dataclass
class MemoryEntry:
    """Single memory entry"""
    id: Optional[int] = None
    timestamp: str = ""
    entry_type: str = ""  # context, fact, tool_result, preference
    key: str = ""
    value: str = ""
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.now().isoformat()
        if self.metadata is None:
            self.metadata = {}


class MemorySystem:
    """Advanced memory management system"""
    
    def __init__(self):
        self.config = get_config().memory
        self.db_path = Path(self.config.database_path)
        self.json_path = Path(self.config.json_backup_path)
        
        if self.config.type == "sqlite":
            self._init_sqlite()
        else:
            self._init_json()
    
    def _init_sqlite(self) -> None:
        """Initialize SQLite database"""
        try:
            self.conn = sqlite3.connect(self.db_path, check_same_thread=False)
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS memory (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    entry_type TEXT NOT NULL,
                    key TEXT NOT NULL,
                    value TEXT NOT NULL,
                    metadata TEXT DEFAULT '{}'
                )
            """)
            
            # Create indexes for better performance
            self.conn.execute("CREATE INDEX IF NOT EXISTS idx_timestamp ON memory(timestamp)")
            self.conn.execute("CREATE INDEX IF NOT EXISTS idx_type ON memory(entry_type)")
            self.conn.execute("CREATE INDEX IF NOT EXISTS idx_key ON memory(key)")
            
            self.conn.commit()
            logging.info("SQLite memory system initialized")
            
        except Exception as e:
            logging.error(f"Failed to initialize SQLite: {e}")
            self._fallback_to_json()
    
    def _init_json(self) -> None:
        """Initialize JSON-based memory"""
        self.memory_data = []
        if self.json_path.exists():
            try:
                with open(self.json_path, 'r') as f:
                    data = json.load(f)
                    self.memory_data = data if isinstance(data, list) else []
            except Exception as e:
                logging.error(f"Failed to load JSON memory: {e}")
                self.memory_data = []
        
        logging.info("JSON memory system initialized")
    
    def _fallback_to_json(self) -> None:
        """Fallback to JSON if SQLite fails"""
        logging.warning("Falling back to JSON memory system")
        self.config.type = "json"
        self._init_json()
    
    def add_entry(self, entry_type: str, key: str, value: str, 
                  metadata: Optional[Dict[str, Any]] = None) -> bool:
        """Add a new memory entry"""
        entry = MemoryEntry(
            entry_type=entry_type,
            key=key,
            value=value,
            metadata=metadata or {}
        )
        
        try:
            if self.config.type == "sqlite":
                return self._add_sqlite_entry(entry)
            else:
                return self._add_json_entry(entry)
        except Exception as e:
            logging.error(f"Failed to add memory entry: {e}")
            return False
    
    def _add_sqlite_entry(self, entry: MemoryEntry) -> bool:
        """Add entry to SQLite database"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("""
                INSERT INTO memory (timestamp, entry_type, key, value, metadata)
                VALUES (?, ?, ?, ?, ?)
            """, (
                entry.timestamp,
                entry.entry_type,
                entry.key,
                entry.value,
                json.dumps(entry.metadata)
            ))
            self.conn.commit()
            
            # Cleanup old entries if needed
            self._cleanup_old_entries()
            return True
            
        except Exception as e:
            logging.error(f"SQLite add error: {e}")
            return False
    
    def _add_json_entry(self, entry: MemoryEntry) -> bool:
        """Add entry to JSON memory"""
        try:
            self.memory_data.append(asdict(entry))
            
            # Keep only recent entries
            if len(self.memory_data) > self.config.max_context_entries:
                self.memory_data = self.memory_data[-self.config.max_context_entries:]
            
            self._save_json()
            return True
            
        except Exception as e:
            logging.error(f"JSON add error: {e}")
            return False
    
    def get_context(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get recent context entries"""
        if limit is None:
            limit = self.config.max_context_entries
        
        try:
            if self.config.type == "sqlite":
                return self._get_sqlite_context(limit)
            else:
                return self._get_json_context(limit)
        except Exception as e:
            logging.error(f"Failed to get context: {e}")
            return []
    
    def _get_sqlite_context(self, limit: int) -> List[Dict[str, Any]]:
        """Get context from SQLite"""
        cursor = self.conn.cursor()
        cursor.execute("""
            SELECT timestamp, entry_type, key, value, metadata
            FROM memory
            WHERE entry_type = 'context'
            ORDER BY timestamp DESC
            LIMIT ?
        """, (limit,))
        
        results = []
        for row in cursor.fetchall():
            results.append({
                'timestamp': row[0],
                'entry_type': row[1],
                'key': row[2],
                'value': row[3],
                'metadata': json.loads(row[4])
            })
        
        return list(reversed(results))  # Return in chronological order
    
    def _get_json_context(self, limit: int) -> List[Dict[str, Any]]:
        """Get context from JSON"""
        context_entries = [
            entry for entry in self.memory_data
            if entry.get('entry_type') == 'context'
        ]
        return context_entries[-limit:] if context_entries else []
    
    def get_fact(self, key: str) -> Optional[str]:
        """Get a specific fact by key"""
        try:
            if self.config.type == "sqlite":
                return self._get_sqlite_fact(key)
            else:
                return self._get_json_fact(key)
        except Exception as e:
            logging.error(f"Failed to get fact '{key}': {e}")
            return None
    
    def _get_sqlite_fact(self, key: str) -> Optional[str]:
        """Get fact from SQLite"""
        cursor = self.conn.cursor()
        cursor.execute("""
            SELECT value FROM memory
            WHERE entry_type = 'fact' AND key = ?
            ORDER BY timestamp DESC
            LIMIT 1
        """, (key,))
        
        result = cursor.fetchone()
        return result[0] if result else None
    
    def _get_json_fact(self, key: str) -> Optional[str]:
        """Get fact from JSON"""
        for entry in reversed(self.memory_data):
            if entry.get('entry_type') == 'fact' and entry.get('key') == key:
                return entry.get('value')
        return None
    
    def set_fact(self, key: str, value: str, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """Set or update a fact"""
        return self.add_entry('fact', key, value, metadata)
    
    def search_facts(self, query: str) -> List[Tuple[str, str]]:
        """Search facts by key or value"""
        try:
            if self.config.type == "sqlite":
                return self._search_sqlite_facts(query)
            else:
                return self._search_json_facts(query)
        except Exception as e:
            logging.error(f"Failed to search facts: {e}")
            return []
    
    def _search_sqlite_facts(self, query: str) -> List[Tuple[str, str]]:
        """Search facts in SQLite"""
        cursor = self.conn.cursor()
        cursor.execute("""
            SELECT DISTINCT key, value FROM memory
            WHERE entry_type = 'fact' AND (key LIKE ? OR value LIKE ?)
            ORDER BY timestamp DESC
        """, (f'%{query}%', f'%{query}%'))
        
        return cursor.fetchall()
    
    def _search_json_facts(self, query: str) -> List[Tuple[str, str]]:
        """Search facts in JSON"""
        results = []
        seen_keys = set()
        
        for entry in reversed(self.memory_data):
            if (entry.get('entry_type') == 'fact' and 
                entry.get('key') not in seen_keys):
                
                key = entry.get('key', '')
                value = entry.get('value', '')
                
                if query.lower() in key.lower() or query.lower() in value.lower():
                    results.append((key, value))
                    seen_keys.add(key)
        
        return results
    
    def add_tool_result(self, tool_name: str, args: Dict[str, Any], 
                       result: str, success: bool = True) -> bool:
        """Add tool execution result to memory"""
        metadata = {
            'tool_name': tool_name,
            'args': args,
            'success': success
        }
        return self.add_entry('tool_result', tool_name, result, metadata)
    
    def add_context(self, user_input: str, agent_response: str) -> bool:
        """Add conversation context"""
        metadata = {
            'user_input': user_input,
            'agent_response': agent_response
        }
        return self.add_entry('context', 'conversation', 
                            f"User: {user_input}\nAgent: {agent_response}", metadata)
    
    def _cleanup_old_entries(self) -> None:
        """Clean up old entries to maintain performance"""
        if self.config.type != "sqlite":
            return
        
        try:
            # Keep only recent context entries
            cursor = self.conn.cursor()
            cursor.execute("""
                DELETE FROM memory
                WHERE entry_type = 'context'
                AND id NOT IN (
                    SELECT id FROM memory
                    WHERE entry_type = 'context'
                    ORDER BY timestamp DESC
                    LIMIT ?
                )
            """, (self.config.max_context_entries,))
            
            # Keep only recent facts (by key)
            cursor.execute("""
                DELETE FROM memory
                WHERE entry_type = 'fact'
                AND id NOT IN (
                    SELECT MAX(id) FROM memory
                    WHERE entry_type = 'fact'
                    GROUP BY key
                    ORDER BY MAX(timestamp) DESC
                    LIMIT ?
                )
            """, (self.config.max_long_term_facts,))
            
            self.conn.commit()
            
        except Exception as e:
            logging.error(f"Cleanup error: {e}")
    
    def _save_json(self) -> None:
        """Save JSON memory to file"""
        try:
            with open(self.json_path, 'w') as f:
                json.dump(self.memory_data, f, indent=2)
        except Exception as e:
            logging.error(f"Failed to save JSON memory: {e}")
    
    def backup_to_json(self) -> bool:
        """Backup SQLite memory to JSON"""
        if self.config.type != "sqlite":
            return True
        
        try:
            cursor = self.conn.cursor()
            cursor.execute("SELECT * FROM memory ORDER BY timestamp")
            
            backup_data = []
            for row in cursor.fetchall():
                backup_data.append({
                    'id': row[0],
                    'timestamp': row[1],
                    'entry_type': row[2],
                    'key': row[3],
                    'value': row[4],
                    'metadata': json.loads(row[5])
                })
            
            with open(self.json_path, 'w') as f:
                json.dump(backup_data, f, indent=2)
            
            logging.info(f"Memory backed up to {self.json_path}")
            return True
            
        except Exception as e:
            logging.error(f"Backup failed: {e}")
            return False
    
    def close(self) -> None:
        """Close memory system connections"""
        if hasattr(self, 'conn'):
            self.conn.close()


# Global memory system instance
memory_system = MemorySystem()


def get_memory() -> MemorySystem:
    """Get the global memory system"""
    return memory_system
