
import json
from tools import list_files, run_shell, read_file
from planner import plan_steps
from prompts import create_prompt
import subprocess

TOOL_REGISTRY = {
    "list_files": list_files,
    "run_shell": run_shell,
    "read_file": read_file,
}

def call_llm(prompt):
    result = subprocess.run(
        ['ollama', 'run', 'mistral'],
        input=prompt.encode(),
        capture_output=True
    )
    return result.stdout.decode()

def execute_steps(steps):
    results = []
    for step in steps:
        tool = step.get("tool")
        args = step.get("args", {})
        if tool in TOOL_REGISTRY:
            try:
                result = TOOL_REGISTRY[tool](**args)
                results.append({"step": step, "result": result})
            except Exception as e:
                results.append({"step": step, "error": str(e)})
    return results

def update_memory(memory, new_entries):
    memory.extend(new_entries)
    with open("memory.json", "w") as f:
        json.dump(memory[-10:], f)

def main():
    try:
        with open("memory.json", "r") as f:
            memory = json.load(f)
    except:
        memory = []

    while True:
        user_input = input("🧠 Agent> ")
        if user_input.lower() in ["exit", "quit"]:
            break

        prompt = create_prompt(user_input, memory, TOOL_REGISTRY)
        llm_response = call_llm(prompt)
        steps = plan_steps(llm_response)
        results = execute_steps(steps)
        update_memory(memory, results)

        for r in results:
            print(f">>> {r.get('result', r.get('error'))}")

if __name__ == "__main__":
    main()
