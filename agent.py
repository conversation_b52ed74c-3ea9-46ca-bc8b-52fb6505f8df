
"""
Enhanced Local Agent Mode System

A comprehensive AI agent that can plan, execute, and reflect on tasks
with configurable autonomy levels and robust error handling.
"""

import subprocess
import logging
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

# Import our enhanced modules
from config_manager import get_config, Config<PERSON>anager
from memory_system import get_memory
from tools import get_tool_registry, execute_tool
from planner import parse_plan, ExecutionPlan, PlanStep
from prompts import (
    create_planning_prompt,
    create_reflection_prompt,
    create_confirmation_prompt
)


@dataclass
class ExecutionResult:
    """Result of executing a plan step"""
    step: PlanStep
    success: bool
    result: str
    execution_time: float
    error: Optional[str] = None


class LLMInterface:
    """Interface for communicating with local LLM via Ollama"""

    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager.get_config().llm
        self.model = self.config.model
        self.timeout = self.config.timeout

    def call_llm(self, prompt: str, max_retries: int = 3) -> str:
        """
        Call the local LLM with retry logic

        Args:
            prompt: The prompt to send to the LLM
            max_retries: Maximum number of retry attempts

        Returns:
            LLM response text
        """
        for attempt in range(max_retries):
            try:
                logging.info(f"Calling LLM (attempt {attempt + 1}/{max_retries})")

                result = subprocess.run(
                    ['ollama', 'run', self.model],
                    input=prompt,
                    capture_output=True,
                    timeout=self.timeout,
                    text=True
                )

                if result.returncode == 0 and result.stdout.strip():
                    response = result.stdout.strip()
                    logging.info(f"LLM response received ({len(response)} chars)")
                    return response
                else:
                    error_msg = result.stderr.strip() if result.stderr else "No output from LLM"
                    logging.warning(f"LLM call failed: {error_msg}")

            except subprocess.TimeoutExpired:
                logging.warning(f"LLM call timed out after {self.timeout} seconds")
            except Exception as e:
                logging.error(f"LLM call error: {e}")

            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)  # Exponential backoff

        raise Exception(f"Failed to get LLM response after {max_retries} attempts")


class AgentExecutor:
    """Handles execution of plans with error handling and retries"""

    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager.get_config()
        self.memory = get_memory()
        self.tool_registry = get_tool_registry()

    def execute_plan(self, plan: ExecutionPlan,
                    autonomy_level: str = "confirm") -> List[ExecutionResult]:
        """
        Execute a plan with appropriate autonomy level

        Args:
            plan: ExecutionPlan to execute
            autonomy_level: Level of autonomy (manual, confirm, auto)

        Returns:
            List of execution results
        """
        results = []

        logging.info(f"Executing plan with {len(plan.steps)} steps (autonomy: {autonomy_level})")

        for i, step in enumerate(plan.steps):
            print(f"\n--- Step {i + 1}/{len(plan.steps)} ---")
            print(f"Tool: {step.tool}")
            print(f"Description: {step.description}")

            # Check autonomy level for confirmation
            if autonomy_level == "manual":
                if not self._get_user_confirmation(step, "manual"):
                    print("Step skipped by user")
                    continue
            elif autonomy_level == "confirm" and self._is_risky_operation(step):
                if not self._get_user_confirmation(step, "confirm"):
                    print("Step skipped by user")
                    continue

            # Execute the step
            result = self._execute_step_with_retry(step)
            results.append(result)

            # Store result in memory
            self.memory.add_tool_result(
                tool_name=step.tool,
                args=step.args,
                result=result.result,
                success=result.success
            )

            # Print result
            if result.success:
                print(f"✅ Success: {result.result[:200]}...")
            else:
                print(f"❌ Error: {result.error}")

                # Ask user how to proceed on error
                if not self._handle_execution_error(step, result, autonomy_level):
                    break  # User chose to stop

        return results

    def _execute_step_with_retry(self, step: PlanStep) -> ExecutionResult:
        """Execute a step with retry logic"""
        start_time = time.time()

        for attempt in range(step.max_retries):
            try:
                logging.info(f"Executing {step.tool} (attempt {attempt + 1})")

                result = execute_tool(step.tool, **step.args)
                execution_time = time.time() - start_time

                # Check if result indicates success
                if not result.startswith("Error"):
                    return ExecutionResult(
                        step=step,
                        success=True,
                        result=result,
                        execution_time=execution_time
                    )
                else:
                    if attempt < step.max_retries - 1:
                        logging.warning(f"Step failed, retrying: {result}")
                        time.sleep(1)
                        continue
                    else:
                        return ExecutionResult(
                            step=step,
                            success=False,
                            result="",
                            execution_time=execution_time,
                            error=result
                        )

            except Exception as e:
                execution_time = time.time() - start_time
                error_msg = str(e)

                if attempt < step.max_retries - 1:
                    logging.warning(f"Step failed with exception, retrying: {error_msg}")
                    time.sleep(1)
                    continue
                else:
                    return ExecutionResult(
                        step=step,
                        success=False,
                        result="",
                        execution_time=execution_time,
                        error=error_msg
                    )

        # Should not reach here, but just in case
        return ExecutionResult(
            step=step,
            success=False,
            result="",
            execution_time=time.time() - start_time,
            error="Maximum retries exceeded"
        )

    def _get_user_confirmation(self, step: PlanStep, mode: str) -> bool:
        """Get user confirmation for step execution"""
        if mode == "manual":
            prompt = f"Execute {step.tool} with args {step.args}? (y/n): "
        else:
            prompt = f"This operation may be risky. Execute {step.tool}? (y/n): "

        while True:
            response = input(prompt).lower().strip()
            if response in ['y', 'yes']:
                return True
            elif response in ['n', 'no']:
                return False
            else:
                print("Please enter 'y' or 'n'")

    def _is_risky_operation(self, step: PlanStep) -> bool:
        """Determine if an operation is risky and needs confirmation"""
        risky_tools = {'delete_file', 'run_shell', 'write_file'}
        return step.tool in risky_tools

    def _handle_execution_error(self, step: PlanStep, result: ExecutionResult,
                               autonomy_level: str) -> bool:
        """Handle execution errors and ask user how to proceed"""
        print(f"\n❌ Step failed: {step.tool}")
        print(f"Error: {result.error}")

        if autonomy_level == "auto":
            print("Continuing with next step (auto mode)")
            return True

        while True:
            choice = input("\nHow to proceed? (c)ontinue, (r)etry, (s)top: ").lower().strip()

            if choice in ['c', 'continue']:
                return True
            elif choice in ['s', 'stop']:
                return False
            elif choice in ['r', 'retry']:
                # Could implement retry with modified parameters here
                print("Retry not implemented yet, continuing...")
                return True
            else:
                print("Please enter 'c', 'r', or 's'")


class LocalAgent:
    """Main agent class that orchestrates planning and execution"""

    def __init__(self, config_path: str = "config.yaml"):
        self.config_manager = ConfigManager(config_path)
        self.config = self.config_manager.get_config()
        self.memory = get_memory()
        self.llm = LLMInterface(self.config_manager)
        self.executor = AgentExecutor(self.config_manager)

        logging.info(f"LocalAgent initialized with autonomy level: {self.config.agent.autonomy_level}")

    def process_request(self, user_input: str) -> None:
        """
        Process a user request through the full agent pipeline

        Args:
            user_input: User's natural language request
        """
        try:
            print(f"\n🧠 Processing: {user_input}")

            # Store user input in memory
            self.memory.add_context(user_input, "Processing...")

            # Create planning prompt
            prompt = create_planning_prompt(user_input, self.config.agent.autonomy_level)

            # Get LLM response
            print("🤔 Planning...")
            llm_response = self.llm.call_llm(prompt)

            # Parse response into execution plan
            plan = parse_plan(llm_response, user_input)

            if not plan.steps:
                print("❌ Could not create a valid execution plan")
                return

            print(f"📋 Created plan with {len(plan.steps)} steps (confidence: {plan.confidence:.2f})")

            # Show plan to user if needed
            if self.config.agent.autonomy_level in ["manual", "confirm"]:
                self._show_plan_summary(plan)

                if self.config.agent.autonomy_level == "manual":
                    if not self._get_plan_approval():
                        print("Plan cancelled by user")
                        return

            # Execute the plan
            print("\n🚀 Executing plan...")
            results = self.executor.execute_plan(plan, self.config.agent.autonomy_level)

            # Update memory with final result
            success_count = sum(1 for r in results if r.success)
            total_time = sum(r.execution_time for r in results)

            summary = f"Executed {success_count}/{len(results)} steps successfully in {total_time:.2f}s"
            self.memory.add_context(user_input, summary)

            print(f"\n✅ Completed: {summary}")

        except Exception as e:
            error_msg = f"Error processing request: {e}"
            logging.error(error_msg)
            print(f"❌ {error_msg}")
            self.memory.add_context(user_input, f"Error: {error_msg}")

    def _show_plan_summary(self, plan: ExecutionPlan) -> None:
        """Show a summary of the execution plan"""
        print(f"\n📋 Execution Plan (estimated time: {plan.estimated_time}s):")
        for i, step in enumerate(plan.steps, 1):
            print(f"  {i}. {step.tool}: {step.description}")

    def _get_plan_approval(self) -> bool:
        """Get user approval for the entire plan"""
        while True:
            response = input("\nApprove this plan? (y/n): ").lower().strip()
            if response in ['y', 'yes']:
                return True
            elif response in ['n', 'no']:
                return False
            else:
                print("Please enter 'y' or 'n'")

    def run_interactive(self) -> None:
        """Run the agent in interactive mode"""
        print(f"🤖 Local Agent Mode v{self.config.agent.version}")
        print(f"Autonomy Level: {self.config.agent.autonomy_level}")
        print("Type 'help' for commands, 'exit' to quit\n")

        while True:
            try:
                user_input = input("🧠 Agent> ").strip()

                if not user_input:
                    continue

                if user_input.lower() in ["exit", "quit", "q"]:
                    print("Goodbye! 👋")
                    break

                elif user_input.lower() == "help":
                    self._show_help()
                    continue

                elif user_input.lower().startswith("autonomy "):
                    new_level = user_input.split(" ", 1)[1]
                    self._change_autonomy_level(new_level)
                    continue

                elif user_input.lower() == "memory":
                    self._show_memory_info()
                    continue

                elif user_input.lower() == "tools":
                    self._show_available_tools()
                    continue

                # Process as normal request
                self.process_request(user_input)

            except KeyboardInterrupt:
                print("\n\nInterrupted by user. Type 'exit' to quit.")
            except Exception as e:
                logging.error(f"Unexpected error in interactive loop: {e}")
                print(f"❌ Unexpected error: {e}")

    def _show_help(self) -> None:
        """Show help information"""
        print("""
🤖 Local Agent Commands:
  help                 - Show this help
  autonomy <level>     - Change autonomy level (manual/confirm/auto)
  memory              - Show memory statistics
  tools               - List available tools
  exit/quit           - Exit the agent

Examples:
  "list files in the current directory"
  "read the config.yaml file"
  "search for 'TODO' in all Python files"
  "create a new directory called 'backup'"
        """)

    def _change_autonomy_level(self, new_level: str) -> None:
        """Change the autonomy level"""
        if new_level in ["manual", "confirm", "auto"]:
            self.config_manager.update_autonomy_level(new_level)
            self.config = self.config_manager.get_config()
            print(f"✅ Autonomy level changed to: {new_level}")
        else:
            print("❌ Invalid autonomy level. Use: manual, confirm, or auto")

    def _show_memory_info(self) -> None:
        """Show memory system information"""
        context = self.memory.get_context(limit=5)
        print(f"\n💾 Memory Info:")
        print(f"Recent context entries: {len(context)}")
        print(f"Memory type: {self.memory.config.type}")

        if context:
            print("\nRecent context:")
            for entry in context[-3:]:
                timestamp = entry.get('timestamp', '')[:19]  # Remove microseconds
                value = entry.get('value', '')[:100]
                print(f"  [{timestamp}] {value}...")

    def _show_available_tools(self) -> None:
        """Show available tools"""
        from tools import get_tool_descriptions

        descriptions = get_tool_descriptions()
        print(f"\n🔧 Available Tools ({len(descriptions)}):")

        for tool_name, description in descriptions.items():
            clean_desc = description.split('\n')[0][:80]
            print(f"  • {tool_name}: {clean_desc}")


def main():
    """Main entry point"""
    try:
        agent = LocalAgent()
        agent.run_interactive()
    except Exception as e:
        logging.error(f"Failed to start agent: {e}")
        print(f"❌ Failed to start agent: {e}")
    finally:
        # Cleanup
        memory = get_memory()
        memory.close()


if __name__ == "__main__":
    main()
