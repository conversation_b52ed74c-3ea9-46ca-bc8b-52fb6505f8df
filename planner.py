
"""
Advanced Planning System for Local Agent Mode

Handles parsing LLM responses into executable plans, validates tool calls,
and provides fallback mechanisms for robust plan execution.
"""

import json
import re
import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from tools import get_tool_registry, get_tool_descriptions


@dataclass
class PlanStep:
    """Represents a single step in an execution plan"""
    tool: str
    args: Dict[str, Any]
    description: str = ""
    retry_count: int = 0
    max_retries: int = 3

    def __post_init__(self):
        if not self.description:
            self.description = f"Execute {self.tool} with args: {self.args}"


@dataclass
class ExecutionPlan:
    """Represents a complete execution plan"""
    steps: List[PlanStep]
    original_request: str = ""
    confidence: float = 1.0
    estimated_time: int = 0  # seconds

    def __len__(self):
        return len(self.steps)

    def __iter__(self):
        return iter(self.steps)


class PlanParser:
    """Parses LLM responses into structured execution plans"""

    def __init__(self):
        self.tool_registry = get_tool_registry()
        self.tool_descriptions = get_tool_descriptions()

    def parse_response(self, response: str, original_request: str = "") -> ExecutionPlan:
        """
        Parse LLM response into an execution plan

        Args:
            response: Raw LLM response text
            original_request: Original user request for context

        Returns:
            ExecutionPlan object with parsed steps
        """
        steps = []
        confidence = 1.0

        # Try multiple parsing strategies
        parsed_steps = (
            self._parse_json_array(response) or
            self._parse_json_objects(response) or
            self._parse_structured_text(response) or
            self._create_fallback_plan(response, original_request)
        )

        # Validate and convert to PlanStep objects
        for step_data in parsed_steps:
            plan_step = self._validate_step(step_data)
            if plan_step:
                steps.append(plan_step)
            else:
                confidence *= 0.8  # Reduce confidence for invalid steps

        return ExecutionPlan(
            steps=steps,
            original_request=original_request,
            confidence=confidence,
            estimated_time=self._estimate_execution_time(steps)
        )

    def _parse_json_array(self, response: str) -> Optional[List[Dict[str, Any]]]:
        """Try to parse response as a JSON array"""
        try:
            # Look for JSON array in the response
            json_match = re.search(r'\[.*?\]', response, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                return json.loads(json_str)
        except (json.JSONDecodeError, AttributeError):
            pass
        return None

    def _parse_json_objects(self, response: str) -> Optional[List[Dict[str, Any]]]:
        """Try to parse multiple JSON objects from response"""
        try:
            steps = []
            # Look for individual JSON objects
            json_objects = re.findall(r'\{[^{}]*\}', response)
            for obj_str in json_objects:
                try:
                    obj = json.loads(obj_str)
                    if 'tool' in obj:
                        steps.append(obj)
                except json.JSONDecodeError:
                    continue
            return steps if steps else None
        except Exception:
            pass
        return None

    def _parse_structured_text(self, response: str) -> Optional[List[Dict[str, Any]]]:
        """Try to parse structured text format"""
        try:
            steps = []
            lines = response.split('\n')

            current_step = {}
            for line in lines:
                line = line.strip()

                # Look for tool specifications
                if line.startswith('Tool:') or line.startswith('tool:'):
                    if current_step:
                        steps.append(current_step)
                    current_step = {'tool': line.split(':', 1)[1].strip()}

                elif line.startswith('Args:') or line.startswith('args:'):
                    try:
                        args_str = line.split(':', 1)[1].strip()
                        current_step['args'] = json.loads(args_str)
                    except:
                        current_step['args'] = {}

                elif line.startswith('Description:') or line.startswith('description:'):
                    current_step['description'] = line.split(':', 1)[1].strip()

            if current_step:
                steps.append(current_step)

            return steps if steps else None
        except Exception:
            pass
        return None

    def _create_fallback_plan(self, response: str, original_request: str) -> List[Dict[str, Any]]:
        """Create a fallback plan when parsing fails"""
        logging.warning("Failed to parse LLM response, creating fallback plan")

        # Try to infer intent from the original request
        request_lower = original_request.lower()

        fallback_steps = []

        # Simple keyword-based fallback
        if any(word in request_lower for word in ['list', 'show', 'files']):
            fallback_steps.append({
                'tool': 'list_files',
                'args': {'detailed': True},
                'description': 'List files in current directory'
            })

        elif any(word in request_lower for word in ['read', 'open', 'view']):
            # Try to extract filename from request
            words = original_request.split()
            for word in words:
                if '.' in word:  # Likely a filename
                    fallback_steps.append({
                        'tool': 'read_file',
                        'args': {'path': word},
                        'description': f'Read file: {word}'
                    })
                    break

        elif any(word in request_lower for word in ['search', 'find']):
            # Extract search query
            query_words = [w for w in original_request.split()
                          if w.lower() not in ['search', 'find', 'for', 'in']]
            if query_words:
                fallback_steps.append({
                    'tool': 'search_files',
                    'args': {'query': ' '.join(query_words)},
                    'description': f'Search for: {" ".join(query_words)}'
                })

        # If no specific action detected, default to listing files
        if not fallback_steps:
            fallback_steps.append({
                'tool': 'list_files',
                'args': {'detailed': True},
                'description': 'List current directory contents'
            })

        return fallback_steps

    def _validate_step(self, step_data: Dict[str, Any]) -> Optional[PlanStep]:
        """Validate and convert step data to PlanStep object"""
        if not isinstance(step_data, dict):
            logging.warning(f"Invalid step data type: {type(step_data)}")
            return None

        tool_name = step_data.get('tool', '').strip()
        if not tool_name:
            logging.warning("Step missing tool name")
            return None

        if tool_name not in self.tool_registry:
            logging.warning(f"Unknown tool: {tool_name}")
            return None

        args = step_data.get('args', {})
        if not isinstance(args, dict):
            logging.warning(f"Invalid args for tool {tool_name}: {args}")
            args = {}

        description = step_data.get('description', '')

        return PlanStep(
            tool=tool_name,
            args=args,
            description=description
        )

    def _estimate_execution_time(self, steps: List[PlanStep]) -> int:
        """Estimate execution time for the plan"""
        # Simple time estimation based on tool types
        time_estimates = {
            'list_files': 1,
            'read_file': 2,
            'write_file': 3,
            'search_files': 5,
            'run_shell': 10,
            'create_directory': 1,
            'delete_file': 2,
            'get_file_info': 1
        }

        total_time = 0
        for step in steps:
            total_time += time_estimates.get(step.tool, 5)  # Default 5 seconds

        return total_time


# Global parser instance
plan_parser = PlanParser()


def parse_plan(response: str, original_request: str = "") -> ExecutionPlan:
    """Parse LLM response into execution plan"""
    return plan_parser.parse_response(response, original_request)


def plan_steps(response: str) -> List[Dict[str, Any]]:
    """Legacy function for backward compatibility"""
    plan = parse_plan(response)
    return [{'tool': step.tool, 'args': step.args} for step in plan.steps]
