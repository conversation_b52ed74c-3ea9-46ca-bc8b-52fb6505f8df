#!/usr/bin/env python3
"""
Setup and Installation Script for Local Agent Mode

This script helps set up the agent system and verify dependencies.
"""

import sys
import subprocess
import shutil
from pathlib import Path


def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    
    print(f"✅ Python version: {sys.version.split()[0]}")
    return True


def check_ollama():
    """Check if Ollama is installed and available"""
    try:
        result = subprocess.run(['ollama', '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Ollama found: {result.stdout.strip()}")
            return True
        else:
            print("❌ Ollama not working properly")
            return False
    except FileNotFoundError:
        print("❌ Ollama not found in PATH")
        print("Please install Ollama from: https://ollama.ai")
        return False


def check_ollama_models():
    """Check available Ollama models"""
    try:
        result = subprocess.run(['ollama', 'list'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            models = result.stdout.strip()
            if models and 'NAME' in models:
                print("✅ Ollama models available:")
                for line in models.split('\n')[1:]:  # Skip header
                    if line.strip():
                        model_name = line.split()[0]
                        print(f"   • {model_name}")
                return True
            else:
                print("⚠️  No Ollama models found")
                print("Recommended: ollama pull mistral")
                return False
    except Exception as e:
        print(f"❌ Error checking models: {e}")
        return False


def install_dependencies():
    """Install Python dependencies"""
    try:
        print("📦 Installing Python dependencies...")
        result = subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'],
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Dependencies installed successfully")
            return True
        else:
            print(f"❌ Failed to install dependencies: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Error installing dependencies: {e}")
        return False


def create_default_config():
    """Create default configuration if it doesn't exist"""
    config_path = Path("config.yaml")
    
    if config_path.exists():
        print("✅ Configuration file already exists")
        return True
    
    try:
        # The config.yaml should already exist, but just in case
        print("⚠️  Configuration file not found")
        print("Please ensure config.yaml exists in the current directory")
        return False
    except Exception as e:
        print(f"❌ Error with configuration: {e}")
        return False


def test_basic_functionality():
    """Test basic agent functionality"""
    try:
        print("🧪 Testing basic functionality...")
        
        # Import and test basic components
        from config_manager import get_config
        from memory_system import get_memory
        from tools import get_tool_registry
        
        config = get_config()
        print(f"   ✓ Configuration loaded (model: {config.llm.model})")
        
        memory = get_memory()
        print(f"   ✓ Memory system initialized ({memory.config.type})")
        
        tools = get_tool_registry()
        print(f"   ✓ Tools loaded ({len(tools)} available)")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        return False


def main():
    """Main setup function"""
    print("🤖 Local Agent Mode Setup")
    print("=" * 40)
    
    all_good = True
    
    # Check requirements
    print("\n📋 Checking Requirements:")
    if not check_python_version():
        all_good = False
    
    if not check_ollama():
        all_good = False
    
    if not check_ollama_models():
        print("   Note: You can install a model later with: ollama pull mistral")
    
    # Install dependencies
    print("\n📦 Installing Dependencies:")
    if not install_dependencies():
        all_good = False
    
    # Check configuration
    print("\n⚙️  Checking Configuration:")
    if not create_default_config():
        all_good = False
    
    # Test functionality
    print("\n🧪 Testing Functionality:")
    if not test_basic_functionality():
        all_good = False
    
    # Summary
    print("\n" + "=" * 40)
    if all_good:
        print("✅ Setup completed successfully!")
        print("\nYou can now run the agent with:")
        print("   python agent.py")
        print("\nOr try the examples:")
        print("   python examples.py --quick")
    else:
        print("❌ Setup completed with issues")
        print("Please resolve the issues above before running the agent")
    
    print("\nFor help and documentation, see README.md")


if __name__ == "__main__":
    main()
