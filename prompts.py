
"""
Advanced Prompt System for Local Agent Mode

Provides sophisticated prompting templates for different scenarios:
- Task planning and execution
- Tool reasoning and selection
- Error reflection and recovery
- Context-aware responses
"""

from typing import Dict, List, Any, Optional
from datetime import datetime
from memory_system import get_memory
from tools import get_tool_descriptions


class PromptTemplates:
    """Collection of prompt templates for different scenarios"""

    @staticmethod
    def planning_prompt(user_input: str, context: List[Dict[str, Any]],
                       tool_descriptions: Dict[str, str],
                       autonomy_level: str = "confirm") -> str:
        """
        Create a comprehensive planning prompt

        Args:
            user_input: User's request
            context: Recent conversation context
            tool_descriptions: Available tools and their descriptions
            autonomy_level: Current autonomy level (manual, confirm, auto)

        Returns:
            Formatted planning prompt
        """

        # Format tool descriptions
        tools_section = "## Available Tools\n"
        for tool_name, description in tool_descriptions.items():
            # Clean up the description
            clean_desc = description.strip().replace('\n', ' ')
            if len(clean_desc) > 100:
                clean_desc = clean_desc[:97] + "..."
            tools_section += f"- **{tool_name}**: {clean_desc}\n"

        # Format recent context
        context_section = "## Recent Context\n"
        if context:
            for entry in context[-5:]:  # Last 5 entries
                if entry.get('entry_type') == 'context':
                    metadata = entry.get('metadata', {})
                    user_msg = metadata.get('user_input', '')
                    agent_msg = metadata.get('agent_response', '')
                    if user_msg and agent_msg:
                        context_section += f"User: {user_msg[:100]}...\n"
                        context_section += f"Agent: {agent_msg[:100]}...\n\n"
        else:
            context_section += "No recent context available.\n"

        # Autonomy level instructions
        autonomy_instructions = {
            "manual": "You must ask for confirmation before executing each step.",
            "confirm": "You should ask for confirmation before executing potentially risky operations.",
            "auto": "You can execute all steps automatically, but explain your reasoning."
        }

        autonomy_note = autonomy_instructions.get(autonomy_level, autonomy_instructions["confirm"])

        return f"""# AI Agent Planning System

You are an intelligent local AI agent designed to help users accomplish tasks through careful planning and tool execution.

{tools_section}

{context_section}

## Current Task
**User Request**: {user_input}

**Autonomy Level**: {autonomy_level} - {autonomy_note}

## Instructions

1. **Analyze the Request**: Break down what the user wants to accomplish
2. **Plan Steps**: Create a logical sequence of tool calls to fulfill the request
3. **Consider Safety**: Be cautious with file operations and system commands
4. **Provide Reasoning**: Explain your thinking process

## Response Format

Think through this step-by-step, then provide your response in this exact format:

### Analysis
[Explain what the user wants and why]

### Plan
[Describe your approach and reasoning]

### Steps
```json
[
  {{
    "tool": "tool_name",
    "args": {{"parameter": "value"}},
    "description": "What this step accomplishes"
  }},
  ...
]
```

### Safety Considerations
[Any safety concerns or confirmations needed]

Remember:
- Be precise with file paths and command arguments
- Validate inputs and handle edge cases
- Explain your reasoning clearly
- Ask for clarification if the request is ambiguous
"""

    @staticmethod
    def reflection_prompt(failed_step: Dict[str, Any], error_message: str,
                         original_request: str, attempt_count: int) -> str:
        """
        Create a reflection prompt for handling failures

        Args:
            failed_step: The step that failed
            error_message: Error message from the failure
            original_request: Original user request
            attempt_count: Number of attempts made

        Returns:
            Formatted reflection prompt
        """

        return f"""# Error Analysis and Recovery

## Failed Operation
**Tool**: {failed_step.get('tool', 'unknown')}
**Arguments**: {failed_step.get('args', {})}
**Error**: {error_message}
**Attempt**: {attempt_count}

## Original Request
{original_request}

## Your Task
Analyze why this operation failed and provide a corrected approach.

### Analysis
1. What went wrong?
2. What might have caused this error?
3. How can we fix or work around this issue?

### Corrected Approach
Provide a new plan that addresses the error:

```json
[
  {{
    "tool": "corrected_tool_name",
    "args": {{"corrected_parameter": "corrected_value"}},
    "description": "How this fixes the issue"
  }}
]
```

### Alternative Approaches
If the direct fix isn't possible, suggest alternative ways to accomplish the goal.

Be specific about what changes you're making and why they should work better.
"""

    @staticmethod
    def confirmation_prompt(steps: List[Dict[str, Any]], risk_level: str = "medium") -> str:
        """
        Create a confirmation prompt for user approval

        Args:
            steps: List of steps to be executed
            risk_level: Assessed risk level (low, medium, high)

        Returns:
            Formatted confirmation prompt
        """

        risk_colors = {
            "low": "🟢",
            "medium": "🟡",
            "high": "🔴"
        }

        risk_icon = risk_colors.get(risk_level, "🟡")

        steps_text = ""
        for i, step in enumerate(steps, 1):
            tool = step.get('tool', 'unknown')
            args = step.get('args', {})
            desc = step.get('description', f'Execute {tool}')

            steps_text += f"{i}. **{tool}** - {desc}\n"
            if args:
                steps_text += f"   Arguments: {args}\n"
            steps_text += "\n"

        return f"""# Execution Confirmation Required

{risk_icon} **Risk Level**: {risk_level.upper()}

## Planned Steps:
{steps_text}

**Do you want to proceed with these steps?**
- Type 'yes' or 'y' to execute all steps
- Type 'step' to execute one step at a time
- Type 'no' or 'n' to cancel
- Type 'modify' to suggest changes
"""

    @staticmethod
    def context_summary_prompt(context_entries: List[Dict[str, Any]]) -> str:
        """
        Create a prompt for summarizing context

        Args:
            context_entries: List of context entries to summarize

        Returns:
            Formatted summary prompt
        """

        context_text = ""
        for entry in context_entries:
            timestamp = entry.get('timestamp', '')
            value = entry.get('value', '')
            context_text += f"[{timestamp}] {value}\n"

        return f"""# Context Summarization

Please provide a concise summary of the following conversation context:

{context_text}

## Summary Requirements:
1. Key topics discussed
2. Important decisions made
3. Current state/progress
4. Any unresolved issues

Keep the summary under 200 words and focus on information that would be useful for future interactions.
"""


class PromptManager:
    """Manages prompt generation and context"""

    def __init__(self):
        self.memory = get_memory()
        self.templates = PromptTemplates()

    def create_planning_prompt(self, user_input: str, autonomy_level: str = "confirm") -> str:
        """Create a planning prompt with current context"""
        context = self.memory.get_context(limit=10)
        tool_descriptions = get_tool_descriptions()

        return self.templates.planning_prompt(
            user_input=user_input,
            context=context,
            tool_descriptions=tool_descriptions,
            autonomy_level=autonomy_level
        )

    def create_reflection_prompt(self, failed_step: Dict[str, Any],
                               error_message: str, original_request: str,
                               attempt_count: int) -> str:
        """Create a reflection prompt for error recovery"""
        return self.templates.reflection_prompt(
            failed_step=failed_step,
            error_message=error_message,
            original_request=original_request,
            attempt_count=attempt_count
        )

    def create_confirmation_prompt(self, steps: List[Dict[str, Any]]) -> str:
        """Create a confirmation prompt with risk assessment"""
        # Simple risk assessment based on tools used
        risk_level = self._assess_risk_level(steps)

        return self.templates.confirmation_prompt(
            steps=steps,
            risk_level=risk_level
        )

    def _assess_risk_level(self, steps: List[Dict[str, Any]]) -> str:
        """Assess risk level of planned steps"""
        high_risk_tools = {'delete_file', 'run_shell'}
        medium_risk_tools = {'write_file', 'create_directory'}

        for step in steps:
            tool = step.get('tool', '')
            if tool in high_risk_tools:
                return "high"
            elif tool in medium_risk_tools:
                return "medium"

        return "low"


# Global prompt manager instance
prompt_manager = PromptManager()


def create_prompt(user_input: str, memory: List[Dict[str, Any]],
                 tools: Dict[str, Any], autonomy_level: str = "confirm") -> str:
    """Legacy function for backward compatibility"""
    return prompt_manager.create_planning_prompt(user_input, autonomy_level)


def create_planning_prompt(user_input: str, autonomy_level: str = "confirm") -> str:
    """Create a planning prompt with current context"""
    return prompt_manager.create_planning_prompt(user_input, autonomy_level)


def create_reflection_prompt(failed_step: Dict[str, Any], error_message: str,
                           original_request: str, attempt_count: int) -> str:
    """Create a reflection prompt for error recovery"""
    return prompt_manager.create_reflection_prompt(
        failed_step, error_message, original_request, attempt_count
    )


def create_confirmation_prompt(steps: List[Dict[str, Any]]) -> str:
    """Create a confirmation prompt for user approval"""
    return prompt_manager.create_confirmation_prompt(steps)
