
def create_prompt(user_input, memory, tools):
    tool_descs = "\n".join([f"{k}: {v.__doc__ or 'no description'}" for k, v in tools.items()])
    history = "\n".join([f"{m['step']['tool']} -> {m.get('result', '')}" for m in memory])

    return f"""
You are a helpful AI assistant. You have access to these tools:

{tool_descs}

Based on the user's request, return a JSON plan of steps to take.

Recent history:
{history}

User input:
{user_input}

Think step by step, then return a plan like this:

[
  {{"tool": "tool_name", "args": {{"arg1": "value"}} }},
  ...
]
"""
