"""
Configuration Manager for Local Agent Mode System

Handles loading and managing configuration from YAML files,
environment variables, and runtime settings.
"""

import yaml
import os
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from pathlib import Path


@dataclass
class AgentConfig:
    """Agent-specific configuration"""
    name: str = "LocalAgent"
    version: str = "1.0.0"
    autonomy_level: str = "confirm"  # manual, confirm, auto
    max_retries: int = 3
    max_steps_per_task: int = 10


@dataclass
class LLMConfig:
    """LLM-specific configuration"""
    model: str = "qwen2.5:7b"
    temperature: float = 0.1
    max_tokens: int = 2048
    timeout: int = 30


@dataclass
class MemoryConfig:
    """Memory system configuration"""
    type: str = "sqlite"
    max_context_entries: int = 50
    max_long_term_facts: int = 1000
    database_path: str = "agent_memory.db"
    json_backup_path: str = "memory.json"


@dataclass
class ToolConfig:
    """Tool system configuration"""
    enabled: list = field(default_factory=lambda: [
        "list_files", "read_file", "write_file", "run_shell",
        "search_files", "create_directory", "delete_file", "get_file_info"
    ])
    shell: Dict[str, Any] = field(default_factory=lambda: {
        "timeout": 30,
        "safe_mode": True,
        "allowed_commands": ["ls", "pwd", "cat", "grep", "find", "python", "pip"]
    })
    file: Dict[str, Any] = field(default_factory=lambda: {
        "max_file_size": 10485760,  # 10MB
        "allowed_extensions": [".txt", ".py", ".json", ".yaml", ".md", ".csv"],
        "backup_on_write": True
    })


@dataclass
class LoggingConfig:
    """Logging configuration"""
    level: str = "INFO"
    file: str = "agent.log"
    max_size: int = 10485760  # 10MB
    backup_count: int = 3


@dataclass
class Config:
    """Main configuration container"""
    agent: AgentConfig = field(default_factory=AgentConfig)
    llm: LLMConfig = field(default_factory=LLMConfig)
    memory: MemoryConfig = field(default_factory=MemoryConfig)
    tools: ToolConfig = field(default_factory=ToolConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)


class ConfigManager:
    """Manages configuration loading and access"""
    
    def __init__(self, config_path: str = "config.yaml"):
        self.config_path = Path(config_path)
        self.config = Config()
        self._load_config()
        self._setup_logging()
    
    def _load_config(self) -> None:
        """Load configuration from YAML file"""
        if not self.config_path.exists():
            logging.warning(f"Config file {self.config_path} not found, using defaults")
            return
        
        try:
            with open(self.config_path, 'r') as f:
                yaml_config = yaml.safe_load(f)
            
            if not yaml_config:
                return
            
            # Load each section
            if 'agent' in yaml_config:
                self.config.agent = AgentConfig(**yaml_config['agent'])
            
            if 'llm' in yaml_config:
                self.config.llm = LLMConfig(**yaml_config['llm'])
            
            if 'memory' in yaml_config:
                self.config.memory = MemoryConfig(**yaml_config['memory'])
            
            if 'tools' in yaml_config:
                tools_data = yaml_config['tools']
                self.config.tools = ToolConfig(
                    enabled=tools_data.get('enabled', self.config.tools.enabled),
                    shell=tools_data.get('shell', self.config.tools.shell),
                    file=tools_data.get('file', self.config.tools.file)
                )
            
            if 'logging' in yaml_config:
                self.config.logging = LoggingConfig(**yaml_config['logging'])
                
        except Exception as e:
            logging.error(f"Error loading config: {e}")
            logging.info("Using default configuration")
    
    def _setup_logging(self) -> None:
        """Setup logging based on configuration"""
        from logging.handlers import RotatingFileHandler
        
        # Create logger
        logger = logging.getLogger()
        logger.setLevel(getattr(logging, self.config.logging.level))
        
        # Clear existing handlers
        logger.handlers.clear()
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_format = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_format)
        logger.addHandler(console_handler)
        
        # File handler
        file_handler = RotatingFileHandler(
            self.config.logging.file,
            maxBytes=self.config.logging.max_size,
            backupCount=self.config.logging.backup_count
        )
        file_handler.setLevel(getattr(logging, self.config.logging.level))
        file_format = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        file_handler.setFormatter(file_format)
        logger.addHandler(file_handler)
    
    def get_config(self) -> Config:
        """Get the current configuration"""
        return self.config
    
    def update_autonomy_level(self, level: str) -> None:
        """Update autonomy level at runtime"""
        if level in ["manual", "confirm", "auto"]:
            self.config.agent.autonomy_level = level
            logging.info(f"Autonomy level updated to: {level}")
        else:
            raise ValueError(f"Invalid autonomy level: {level}")
    
    def is_tool_enabled(self, tool_name: str) -> bool:
        """Check if a tool is enabled"""
        return tool_name in self.config.tools.enabled
    
    def get_tool_config(self, tool_type: str) -> Dict[str, Any]:
        """Get configuration for a specific tool type"""
        return getattr(self.config.tools, tool_type, {})


# Global config manager instance
config_manager = ConfigManager()


def get_config() -> Config:
    """Get the global configuration"""
    return config_manager.get_config()


def get_tool_config(tool_type: str) -> Dict[str, Any]:
    """Get tool-specific configuration"""
    return config_manager.get_tool_config(tool_type)


def is_tool_enabled(tool_name: str) -> bool:
    """Check if a tool is enabled"""
    return config_manager.is_tool_enabled(tool_name)
