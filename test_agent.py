#!/usr/bin/env python3
"""
Test Suite for Local Agent Mode System

Comprehensive tests to verify all components work correctly.
"""

import unittest
import tempfile
import shutil
from pathlib import Path
import sys
import os

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

from config_manager import ConfigManager, get_config
from memory_system import MemorySystem
from tools import execute_tool, get_tool_registry
from planner import parse_plan
from prompts import create_planning_prompt


class TestConfigManager(unittest.TestCase):
    """Test configuration management"""
    
    def test_default_config(self):
        """Test default configuration loading"""
        config = get_config()
        self.assertIsNotNone(config)
        self.assertIn(config.agent.autonomy_level, ["manual", "confirm", "auto"])
        self.assertGreater(config.agent.max_retries, 0)
    
    def test_config_validation(self):
        """Test configuration validation"""
        config_manager = ConfigManager()
        
        # Test valid autonomy level
        config_manager.update_autonomy_level("auto")
        self.assertEqual(config_manager.get_config().agent.autonomy_level, "auto")
        
        # Test invalid autonomy level
        with self.assertRaises(ValueError):
            config_manager.update_autonomy_level("invalid")


class TestMemorySystem(unittest.TestCase):
    """Test memory system functionality"""
    
    def setUp(self):
        """Set up test memory system"""
        self.temp_dir = tempfile.mkdtemp()
        self.memory = MemorySystem()
        # Override paths for testing
        self.memory.db_path = Path(self.temp_dir) / "test_memory.db"
        self.memory.json_path = Path(self.temp_dir) / "test_memory.json"
        self.memory._init_sqlite()
    
    def tearDown(self):
        """Clean up test files"""
        if hasattr(self.memory, 'conn'):
            self.memory.conn.close()
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_add_and_get_fact(self):
        """Test adding and retrieving facts"""
        self.memory.set_fact("test_key", "test_value")
        result = self.memory.get_fact("test_key")
        self.assertEqual(result, "test_value")
    
    def test_search_facts(self):
        """Test fact searching"""
        self.memory.set_fact("project_name", "Test Project")
        self.memory.set_fact("project_version", "1.0.0")
        
        results = self.memory.search_facts("project")
        self.assertGreater(len(results), 0)
        
        # Check that results contain our facts
        fact_keys = [result[0] for result in results]
        self.assertIn("project_name", fact_keys)
    
    def test_context_management(self):
        """Test context management"""
        self.memory.add_context("test input", "test response")
        context = self.memory.get_context(limit=1)
        
        self.assertEqual(len(context), 1)
        self.assertEqual(context[0]['entry_type'], 'context')


class TestTools(unittest.TestCase):
    """Test tool system functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_file = Path(self.temp_dir) / "test.txt"
        self.test_content = "This is a test file\nWith multiple lines\nFor testing purposes"
    
    def tearDown(self):
        """Clean up test files"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_tool_registry(self):
        """Test tool registry"""
        registry = get_tool_registry()
        self.assertIsInstance(registry, dict)
        self.assertGreater(len(registry), 0)
        
        # Check for essential tools
        essential_tools = ["list_files", "read_file", "write_file"]
        for tool in essential_tools:
            self.assertIn(tool, registry)
    
    def test_write_and_read_file(self):
        """Test file writing and reading"""
        # Write file
        result = execute_tool("write_file", path=str(self.test_file), content=self.test_content)
        self.assertIn("Successfully", result)
        self.assertTrue(self.test_file.exists())
        
        # Read file
        result = execute_tool("read_file", path=str(self.test_file))
        self.assertEqual(result, self.test_content)
    
    def test_list_files(self):
        """Test file listing"""
        # Create test file
        self.test_file.write_text(self.test_content)
        
        # List files
        result = execute_tool("list_files", path=str(self.temp_dir))
        self.assertIn("test.txt", result)
    
    def test_search_files(self):
        """Test file searching"""
        # Create test file
        self.test_file.write_text(self.test_content)
        
        # Search for content
        result = execute_tool("search_files", path=str(self.temp_dir), query="test")
        self.assertIn("test.txt", result)
        self.assertIn("This is a test file", result)
    
    def test_get_file_info(self):
        """Test file information"""
        # Create test file
        self.test_file.write_text(self.test_content)
        
        # Get file info
        result = execute_tool("get_file_info", path=str(self.test_file))
        self.assertIn("File", result)
        self.assertIn("Size:", result)
        self.assertIn("Modified:", result)
    
    def test_create_directory(self):
        """Test directory creation"""
        test_dir = Path(self.temp_dir) / "new_directory"
        
        result = execute_tool("create_directory", path=str(test_dir))
        self.assertIn("successfully", result)
        self.assertTrue(test_dir.exists())
        self.assertTrue(test_dir.is_dir())


class TestPlanner(unittest.TestCase):
    """Test planning system"""
    
    def test_parse_valid_json(self):
        """Test parsing valid JSON plan"""
        response = '''
        Here's my plan:
        [
            {"tool": "list_files", "args": {"path": "."}, "description": "List files"},
            {"tool": "read_file", "args": {"path": "test.txt"}, "description": "Read file"}
        ]
        '''
        
        plan = parse_plan(response, "test request")
        self.assertEqual(len(plan.steps), 2)
        self.assertEqual(plan.steps[0].tool, "list_files")
        self.assertEqual(plan.steps[1].tool, "read_file")
    
    def test_parse_invalid_response(self):
        """Test parsing invalid response with fallback"""
        response = "This is not a valid JSON response"
        
        plan = parse_plan(response, "list files")
        # Should create fallback plan
        self.assertGreater(len(plan.steps), 0)
        self.assertLess(plan.confidence, 1.0)
    
    def test_plan_validation(self):
        """Test plan step validation"""
        response = '''
        [
            {"tool": "list_files", "args": {"path": "."}},
            {"tool": "invalid_tool", "args": {}},
            {"tool": "read_file", "args": {"path": "test.txt"}}
        ]
        '''
        
        plan = parse_plan(response, "test request")
        # Should filter out invalid tool
        valid_tools = [step.tool for step in plan.steps]
        self.assertIn("list_files", valid_tools)
        self.assertIn("read_file", valid_tools)
        self.assertNotIn("invalid_tool", valid_tools)


class TestPrompts(unittest.TestCase):
    """Test prompt system"""
    
    def test_planning_prompt_creation(self):
        """Test planning prompt creation"""
        prompt = create_planning_prompt("list files", "confirm")
        
        self.assertIsInstance(prompt, str)
        self.assertIn("list files", prompt)
        self.assertIn("Available Tools", prompt)
        self.assertIn("confirm", prompt)
    
    def test_prompt_contains_tools(self):
        """Test that prompt contains tool information"""
        prompt = create_planning_prompt("test request", "auto")
        
        # Should contain some tool names
        self.assertIn("list_files", prompt)
        self.assertIn("read_file", prompt)


class TestIntegration(unittest.TestCase):
    """Integration tests for the complete system"""
    
    def setUp(self):
        """Set up integration test environment"""
        self.temp_dir = tempfile.mkdtemp()
        os.chdir(self.temp_dir)
        
        # Create some test files
        (Path(self.temp_dir) / "test1.txt").write_text("Test file 1 content")
        (Path(self.temp_dir) / "test2.py").write_text("# Python test file\nprint('hello')")
    
    def tearDown(self):
        """Clean up integration test"""
        os.chdir(Path(__file__).parent)
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_end_to_end_file_operations(self):
        """Test end-to-end file operations"""
        # List files
        result = execute_tool("list_files", path=".")
        self.assertIn("test1.txt", result)
        self.assertIn("test2.py", result)
        
        # Read a file
        result = execute_tool("read_file", path="test1.txt")
        self.assertEqual(result, "Test file 1 content")
        
        # Search files
        result = execute_tool("search_files", path=".", query="hello")
        self.assertIn("test2.py", result)
        self.assertIn("hello", result)


def run_tests():
    """Run all tests"""
    print("🧪 Running Local Agent Mode Tests")
    print("=" * 40)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestConfigManager,
        TestMemorySystem,
        TestTools,
        TestPlanner,
        TestPrompts,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = loader.loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 40)
    if result.wasSuccessful():
        print("✅ All tests passed!")
    else:
        print(f"❌ {len(result.failures)} failures, {len(result.errors)} errors")
        
        if result.failures:
            print("\nFailures:")
            for test, traceback in result.failures:
                print(f"  • {test}: {traceback.split('AssertionError:')[-1].strip()}")
        
        if result.errors:
            print("\nErrors:")
            for test, traceback in result.errors:
                print(f"  • {test}: {traceback.split('Exception:')[-1].strip()}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
